import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
} from "react-native";
import {
  ArrowLeft,
  Zap,
  Clock,
  CheckCircle,
  Star,
  TrendingUp,
  Users,
  BarChart3,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import ConsultaExpressPrototype from "../components/ConsultaExpressPrototype";

const ConsultaExpressDemo = () => {
  const router = useRouter();
  const [showDemo, setShowDemo] = useState(false);
  const [demoStats, setDemoStats] = useState({
    consultasHoy: 12,
    tiempoPromedio: 2.3,
    satisfaccion: 4.8,
    ahorro: 85,
  });

  const handleStartDemo = () => {
    Alert.alert(
      "Demo del Prototipo",
      "Vas a probar la nueva experiencia de Consulta Express. Este es un prototipo funcional que demuestra el flujo optimizado de 3 pasos.",
      [
        { text: "Cancelar", style: "cancel" },
        { text: "Comenzar Demo", onPress: () => setShowDemo(true) },
      ]
    );
  };

  const handleBackToMenu = () => {
    setShowDemo(false);
  };

  if (showDemo) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <StatusBar barStyle="dark-content" backgroundColor="#F9FAFB" />
        
        {/* Header del demo */}
        <View className="bg-white px-4 py-3 shadow-sm border-b border-gray-100">
          <View className="flex-row items-center justify-between">
            <TouchableOpacity
              onPress={handleBackToMenu}
              className="flex-row items-center"
            >
              <ArrowLeft size={24} color="#374151" />
              <Text className="text-gray-800 font-semibold ml-2">Volver</Text>
            </TouchableOpacity>
            
            <View className="bg-blue-100 px-3 py-1 rounded-full">
              <Text className="text-blue-800 font-semibold text-sm">
                🚀 PROTOTIPO DEMO
              </Text>
            </View>
          </View>
        </View>

        {/* Prototipo */}
        <ConsultaExpressPrototype />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#F9FAFB" />
      
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8">
          <View className="flex-row items-center mb-4">
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color="white" />
            </TouchableOpacity>
            <Text className="text-white text-xl font-bold ml-4">
              Consulta Express AI
            </Text>
          </View>
          
          <Text className="text-blue-100 text-lg mb-2">
            Prototipo de Nueva Experiencia
          </Text>
          <Text className="text-blue-50">
            Consulta de coloración en 3 pasos y menos de 3 minutos
          </Text>
        </View>

        {/* Estadísticas actuales */}
        <View className="px-6 py-6">
          <Text className="text-xl font-bold text-gray-800 mb-4">
            📊 Rendimiento Actual
          </Text>
          
          <View className="flex-row flex-wrap justify-between mb-6">
            <View className="w-[48%] bg-white p-4 rounded-lg shadow-sm border border-gray-100 mb-4">
              <View className="flex-row items-center mb-2">
                <Users size={20} color="#3B82F6" />
                <Text className="text-blue-600 font-semibold ml-2">Hoy</Text>
              </View>
              <Text className="text-2xl font-bold text-gray-800">{demoStats.consultasHoy}</Text>
              <Text className="text-gray-600 text-sm">Consultas completadas</Text>
            </View>

            <View className="w-[48%] bg-white p-4 rounded-lg shadow-sm border border-gray-100 mb-4">
              <View className="flex-row items-center mb-2">
                <Clock size={20} color="#10B981" />
                <Text className="text-green-600 font-semibold ml-2">Tiempo</Text>
              </View>
              <Text className="text-2xl font-bold text-gray-800">{demoStats.tiempoPromedio}min</Text>
              <Text className="text-gray-600 text-sm">Promedio por consulta</Text>
            </View>

            <View className="w-[48%] bg-white p-4 rounded-lg shadow-sm border border-gray-100 mb-4">
              <View className="flex-row items-center mb-2">
                <Star size={20} color="#F59E0B" />
                <Text className="text-yellow-600 font-semibold ml-2">Satisfacción</Text>
              </View>
              <Text className="text-2xl font-bold text-gray-800">{demoStats.satisfaccion}/5</Text>
              <Text className="text-gray-600 text-sm">Valoración clientes</Text>
            </View>

            <View className="w-[48%] bg-white p-4 rounded-lg shadow-sm border border-gray-100 mb-4">
              <View className="flex-row items-center mb-2">
                <TrendingUp size={20} color="#8B5CF6" />
                <Text className="text-purple-600 font-semibold ml-2">Eficiencia</Text>
              </View>
              <Text className="text-2xl font-bold text-gray-800">{demoStats.ahorro}%</Text>
              <Text className="text-gray-600 text-sm">Tiempo ahorrado</Text>
            </View>
          </View>
        </View>

        {/* Características del prototipo */}
        <View className="px-6 pb-6">
          <Text className="text-xl font-bold text-gray-800 mb-4">
            🚀 Nuevo Prototipo: Consulta Express
          </Text>

          <View className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 mb-6">
            <Text className="text-lg font-semibold text-gray-800 mb-4">
              Mejoras Implementadas:
            </Text>

            <View className="space-y-3">
              <View className="flex-row items-center">
                <CheckCircle size={20} color="#10B981" />
                <Text className="text-gray-700 ml-3 flex-1">
                  <Text className="font-semibold">Flujo 3-Tap:</Text> Cliente → Foto → Objetivo → Resultado
                </Text>
              </View>

              <View className="flex-row items-center">
                <CheckCircle size={20} color="#10B981" />
                <Text className="text-gray-700 ml-3 flex-1">
                  <Text className="font-semibold">Búsqueda Inteligente:</Text> Encuentra clientes al instante
                </Text>
              </View>

              <View className="flex-row items-center">
                <CheckCircle size={20} color="#10B981" />
                <Text className="text-gray-700 ml-3 flex-1">
                  <Text className="font-semibold">Captura Smart:</Text> Feedback en tiempo real
                </Text>
              </View>

              <View className="flex-row items-center">
                <CheckCircle size={20} color="#10B981" />
                <Text className="text-gray-700 ml-3 flex-1">
                  <Text className="font-semibold">Objetivos Visuales:</Text> Selección rápida y clara
                </Text>
              </View>

              <View className="flex-row items-center">
                <CheckCircle size={20} color="#10B981" />
                <Text className="text-gray-700 ml-3 flex-1">
                  <Text className="font-semibold">Resultado Instantáneo:</Text> Fórmula completa y accionable
                </Text>
              </View>

              <View className="flex-row items-center">
                <CheckCircle size={20} color="#10B981" />
                <Text className="text-gray-700 ml-3 flex-1">
                  <Text className="font-semibold">Patrón Single Pane:</Text> Todo en una pantalla
                </Text>
              </View>
            </View>
          </View>

          {/* Comparación */}
          <View className="bg-blue-50 rounded-lg border border-blue-200 p-6 mb-6">
            <Text className="text-blue-800 font-semibold text-lg mb-4">
              📈 Mejoras Esperadas
            </Text>

            <View className="space-y-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-blue-700">Tiempo por consulta:</Text>
                <View className="flex-row items-center">
                  <Text className="text-blue-600 line-through mr-2">10+ min</Text>
                  <Text className="text-blue-800 font-bold">→ 2-3 min</Text>
                </View>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-blue-700">Pasos del proceso:</Text>
                <View className="flex-row items-center">
                  <Text className="text-blue-600 line-through mr-2">8+ pasos</Text>
                  <Text className="text-blue-800 font-bold">→ 3 pasos</Text>
                </View>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-blue-700">Facilidad de uso:</Text>
                <View className="flex-row items-center">
                  <Text className="text-blue-600 line-through mr-2">Complejo</Text>
                  <Text className="text-blue-800 font-bold">→ Intuitivo</Text>
                </View>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-blue-700">Adopción esperada:</Text>
                <View className="flex-row items-center">
                  <Text className="text-blue-600 line-through mr-2">60%</Text>
                  <Text className="text-blue-800 font-bold">→ 90%+</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Botón de demo */}
          <TouchableOpacity
            className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 rounded-lg shadow-lg"
            onPress={handleStartDemo}
          >
            <View className="flex-row items-center justify-center mb-2">
              <Zap size={28} color="white" />
              <Text className="text-white text-xl font-bold ml-3">
                Probar Prototipo
              </Text>
            </View>
            <Text className="text-blue-100 text-center">
              Experimenta la nueva experiencia de consulta express
            </Text>
          </TouchableOpacity>

          {/* Nota importante */}
          <View className="bg-yellow-50 rounded-lg border border-yellow-200 p-4 mt-6">
            <Text className="text-yellow-800 font-semibold mb-2">
              📝 Nota del Prototipo
            </Text>
            <Text className="text-yellow-700 text-sm">
              Este es un prototipo funcional para validar la nueva experiencia de usuario. 
              Todas las funcionalidades están simuladas pero representan fielmente el comportamiento final.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ConsultaExpressDemo;
