import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import {
  ArrowLeft,
  TrendingUp,
  Users,
  Calendar,
  DollarSign,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Clock,
  Star,
} from "lucide-react-native";

const { width } = Dimensions.get("window");

export default function Analytics() {
  const router = useRouter();
  const [selectedPeriod, setSelectedPeriod] = useState("month");

  // Mock data - en el futuro vendrá de Supabase
  const kpis = {
    revenue: { value: "€2,450", change: "+12%", trend: "up" },
    clients: { value: "47", change: "+8%", trend: "up" },
    appointments: { value: "156", change: "+15%", trend: "up" },
    avgTicket: { value: "€52", change: "+3%", trend: "up" },
  };

  const topServices = [
    { name: "Balayage", count: 23, revenue: "€1,150", color: "#8B5CF6" },
    { name: "Color Global", count: 18, revenue: "€900", color: "#EC4899" },
    { name: "Mechas", count: 15, revenue: "€750", color: "#06B6D4" },
    { name: "Tratamiento", count: 12, revenue: "€480", color: "#10B981" },
  ];

  const recentActivity = [
    { type: "appointment", client: "María López", service: "Balayage", time: "2h ago" },
    { type: "new_client", client: "Ana García", service: "Consulta", time: "4h ago" },
    { type: "completion", client: "Laura Ruiz", service: "Color Global", time: "6h ago" },
  ];

  const periods = [
    { id: "week", label: "Semana" },
    { id: "month", label: "Mes" },
    { id: "quarter", label: "Trimestre" },
    { id: "year", label: "Año" },
  ];

  const KPICard = ({ title, value, change, trend, icon: Icon }) => (
    <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 flex-1 mx-1">
      <View className="flex-row items-center justify-between mb-2">
        <Icon size={20} color="#6B7280" />
        <View className={`px-2 py-1 rounded-full ${trend === 'up' ? 'bg-green-100' : 'bg-red-100'}`}>
          <Text className={`text-xs font-medium ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
            {change}
          </Text>
        </View>
      </View>
      <Text className="text-2xl font-bold text-gray-800">{value}</Text>
      <Text className="text-sm text-gray-500 mt-1">{title}</Text>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="flex-row items-center justify-between px-4 py-3 bg-white border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()}>
          <ArrowLeft size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-bold text-gray-800">Analíticas</Text>
        <View className="w-6" />
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Period Selector */}
        <View className="px-4 py-4">
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row space-x-2">
              {periods.map((period) => (
                <TouchableOpacity
                  key={period.id}
                  onPress={() => setSelectedPeriod(period.id)}
                  className={`px-4 py-2 rounded-full ${
                    selectedPeriod === period.id
                      ? "bg-indigo-500"
                      : "bg-white border border-gray-200"
                  }`}
                >
                  <Text
                    className={`font-medium ${
                      selectedPeriod === period.id ? "text-white" : "text-gray-600"
                    }`}
                  >
                    {period.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* KPIs */}
        <View className="px-4 mb-6">
          <Text className="text-lg font-bold text-gray-800 mb-4">Resumen General</Text>
          <View className="flex-row mb-4">
            <KPICard
              title="Ingresos"
              value={kpis.revenue.value}
              change={kpis.revenue.change}
              trend={kpis.revenue.trend}
              icon={DollarSign}
            />
            <KPICard
              title="Clientes"
              value={kpis.clients.value}
              change={kpis.clients.change}
              trend={kpis.clients.trend}
              icon={Users}
            />
          </View>
          <View className="flex-row">
            <KPICard
              title="Citas"
              value={kpis.appointments.value}
              change={kpis.appointments.change}
              trend={kpis.appointments.trend}
              icon={Calendar}
            />
            <KPICard
              title="Ticket Medio"
              value={kpis.avgTicket.value}
              change={kpis.avgTicket.change}
              trend={kpis.avgTicket.trend}
              icon={TrendingUp}
            />
          </View>
        </View>

        {/* Top Services */}
        <View className="px-4 mb-6">
          <Text className="text-lg font-bold text-gray-800 mb-4">Servicios Populares</Text>
          <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            {topServices.map((service, index) => (
              <View key={service.name} className="flex-row items-center justify-between py-3">
                <View className="flex-row items-center flex-1">
                  <View
                    className="w-3 h-3 rounded-full mr-3"
                    style={{ backgroundColor: service.color }}
                  />
                  <View className="flex-1">
                    <Text className="font-medium text-gray-800">{service.name}</Text>
                    <Text className="text-sm text-gray-500">{service.count} servicios</Text>
                  </View>
                </View>
                <Text className="font-bold text-gray-800">{service.revenue}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Recent Activity */}
        <View className="px-4 mb-6">
          <Text className="text-lg font-bold text-gray-800 mb-4">Actividad Reciente</Text>
          <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            {recentActivity.map((activity, index) => (
              <View key={index} className="flex-row items-center py-3">
                <View className="w-8 h-8 rounded-full bg-indigo-100 items-center justify-center mr-3">
                  {activity.type === "appointment" && <Calendar size={16} color="#6366F1" />}
                  {activity.type === "new_client" && <Users size={16} color="#6366F1" />}
                  {activity.type === "completion" && <Star size={16} color="#6366F1" />}
                </View>
                <View className="flex-1">
                  <Text className="font-medium text-gray-800">{activity.client}</Text>
                  <Text className="text-sm text-gray-500">{activity.service}</Text>
                </View>
                <Text className="text-xs text-gray-400">{activity.time}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View className="px-4 mb-8">
          <Text className="text-lg font-bold text-gray-800 mb-4">Acciones Rápidas</Text>
          <View className="flex-row justify-between">
            <TouchableOpacity className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 flex-1 mr-2">
              <BarChart3 size={24} color="#6366F1" />
              <Text className="font-medium text-gray-800 mt-2">Exportar Datos</Text>
            </TouchableOpacity>
            <TouchableOpacity className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 flex-1 ml-2">
              <PieChart size={24} color="#6366F1" />
              <Text className="font-medium text-gray-800 mt-2">Reportes Detallados</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
