import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Modal,
  TextInput,
  Alert,
} from "react-native";
import {
  Calendar,
  Clock,
  User,
  X,
  ChevronLeft,
  ChevronRight,
  Plus,
  Filter,
  Save,
  ChevronDown,
} from "lucide-react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

type Appointment = {
  id: string;
  clientName: string;
  service: string;
  date: string;
  time: string;
  duration: number; // in minutes
  stylist: string;
  status: "confirmed" | "pending" | "cancelled";
  color: string; // for color-coding by service type
};

type CalendarView = "daily" | "weekly" | "monthly";

type AppointmentCalendarProps = {
  appointments?: Appointment[];
  stylists?: string[];
  onCreateAppointment?: () => void;
  onViewAppointment?: (appointment: Appointment) => void;
  onReschedule?: (appointment: Appointment) => void;
  onCancel?: (appointment: Appointment) => void;
  onMarkCompleted?: (appointment: Appointment) => void;
};

const AppointmentCalendar = ({
  appointments: initialAppointments = [
    {
      id: "1",
      clientName: "María García",
      service: "Coloración Completa",
      date: "2023-06-15",
      time: "10:00",
      duration: 120,
      stylist: "Ana Rodríguez",
      status: "confirmed",
      color: "#FFD1DC", // pink for color services
    },
    {
      id: "2",
      clientName: "<PERSON> López",
      service: "Mechas Balayage",
      date: "2023-06-15",
      time: "13:00",
      duration: 150,
      stylist: "Ana Rodríguez",
      status: "confirmed",
      color: "#FFD1DC",
    },
    {
      id: "3",
      clientName: "Laura Martínez",
      service: "Retoque de Raíz",
      date: "2023-06-16",
      time: "11:00",
      duration: 90,
      stylist: "Miguel Sánchez",
      status: "pending",
      color: "#FFD1DC",
    },
    {
      id: "4",
      clientName: "Javier Fernández",
      service: "Consulta de Color",
      date: "2023-06-17",
      time: "15:30",
      duration: 60,
      stylist: "Ana Rodríguez",
      status: "confirmed",
      color: "#D1F0FF", // blue for consultations
    },
  ],
  stylists = ["Ana Rodríguez", "Miguel Sánchez", "Carmen Díaz"],
  onCreateAppointment = () => {},
  onViewAppointment = () => {},
  onReschedule = () => {},
  onCancel = () => {},
  onMarkCompleted = () => {},
}: AppointmentCalendarProps) => {
  const [appointments, setAppointments] =
    useState<Appointment[]>(initialAppointments);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [calendarView, setCalendarView] = useState<CalendarView>("daily");
  const [selectedStylist, setSelectedStylist] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newAppointment, setNewAppointment] = useState({
    clientName: "",
    service: "",
    date: "",
    time: "",
    duration: 60,
    stylist: "",
    status: "confirmed" as const,
  });

  const services = [
    { name: "Coloración Completa", duration: 120, color: "#FFD1DC" },
    { name: "Mechas Balayage", duration: 150, color: "#FFD1DC" },
    { name: "Retoque de Raíz", duration: 90, color: "#FFD1DC" },
    { name: "Consulta de Color", duration: 60, color: "#D1F0FF" },
    { name: "Corte y Peinado", duration: 45, color: "#D1FFD1" },
    { name: "Tratamiento Capilar", duration: 75, color: "#FFFACD" },
  ];

  // Load appointments from storage on component mount
  useEffect(() => {
    loadAppointments();
  }, []);

  const loadAppointments = async () => {
    try {
      const storedAppointments = await AsyncStorage.getItem("appointments");
      if (storedAppointments) {
        setAppointments(JSON.parse(storedAppointments));
      }
    } catch (error) {
      console.error("Error loading appointments:", error);
    }
  };

  const saveAppointments = async (updatedAppointments: Appointment[]) => {
    try {
      await AsyncStorage.setItem(
        "appointments",
        JSON.stringify(updatedAppointments),
      );
      setAppointments(updatedAppointments);
    } catch (error) {
      console.error("Error saving appointments:", error);
      Alert.alert("Error", "No se pudo guardar la cita. Inténtalo de nuevo.");
    }
  };

  // Generate dates for the current week
  const generateWeekDates = () => {
    const dates = [];
    const currentDate = new Date(selectedDate);
    const day = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Set to the beginning of the week (Sunday)
    currentDate.setDate(currentDate.getDate() - day);

    for (let i = 0; i < 7; i++) {
      const date = new Date(currentDate);
      dates.push(date);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  };

  // Generate dates for the current month view
  const generateMonthDates = () => {
    const dates = [];
    const year = selectedDate.getFullYear();
    const month = selectedDate.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);

    // Get the day of the week for the first day (0 = Sunday, 1 = Monday, etc.)
    const firstDayOfWeek = firstDay.getDay();

    // Add days from previous month to fill the first week
    const prevMonth = new Date(year, month, 0);
    const prevMonthDays = prevMonth.getDate();

    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(year, month - 1, prevMonthDays - i);
      dates.push(date);
    }

    // Add all days of the current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      dates.push(date);
    }

    // Add days from next month to complete the last week
    const remainingDays = 42 - dates.length; // 6 rows of 7 days
    for (let i = 1; i <= remainingDays; i++) {
      const date = new Date(year, month + 1, i);
      dates.push(date);
    }

    return dates;
  };

  // Filter appointments for the selected date and stylist
  const getFilteredAppointments = () => {
    const dateStr = selectedDate.toISOString().split("T")[0];
    return appointments.filter((appointment) => {
      const dateMatch = appointment.date === dateStr;
      const stylistMatch = selectedStylist
        ? appointment.stylist === selectedStylist
        : true;
      return dateMatch && stylistMatch;
    });
  };

  // Navigate to previous day/week/month
  const navigatePrevious = () => {
    const newDate = new Date(selectedDate);
    if (calendarView === "daily") {
      newDate.setDate(newDate.getDate() - 1);
    } else if (calendarView === "weekly") {
      newDate.setDate(newDate.getDate() - 7);
    } else {
      newDate.setMonth(newDate.getMonth() - 1);
    }
    setSelectedDate(newDate);
  };

  // Navigate to next day/week/month
  const navigateNext = () => {
    const newDate = new Date(selectedDate);
    if (calendarView === "daily") {
      newDate.setDate(newDate.getDate() + 1);
    } else if (calendarView === "weekly") {
      newDate.setDate(newDate.getDate() + 7);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setSelectedDate(newDate);
  };

  // Format time display (e.g., "10:00" to "10:00 AM")
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":");
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? "PM" : "AM";
    const formattedHour = hour % 12 || 12;
    return `${formattedHour}:${minutes} ${period}`;
  };

  // Calculate appointment position and height for daily view
  const getAppointmentStyle = (appointment: Appointment) => {
    const [hours, minutes] = appointment.time.split(":").map(Number);
    const startMinutes = hours * 60 + minutes;
    const top = (startMinutes - 8 * 60) / 2; // Start at 8 AM, each minute is 0.5px
    const height = appointment.duration / 2; // Each minute is 0.5px

    return {
      top,
      height,
      backgroundColor: appointment.color,
      opacity: appointment.status === "cancelled" ? 0.5 : 1,
    };
  };

  // Render daily view
  const renderDailyView = () => {
    const filteredAppointments = getFilteredAppointments();
    const hours = Array.from({ length: 12 }, (_, i) => i + 8); // 8 AM to 7 PM

    return (
      <View className="flex-1">
        <ScrollView className="flex-1">
          <View className="relative">
            {/* Time slots */}
            {hours.map((hour) => (
              <View
                key={hour}
                className="flex-row border-b border-gray-200 h-16"
              >
                <View className="w-16 items-center justify-center border-r border-gray-200">
                  <Text className="text-gray-500">
                    {hour % 12 || 12} {hour >= 12 ? "PM" : "AM"}
                  </Text>
                </View>
                <View className="flex-1 h-16" />
              </View>
            ))}

            {/* Appointments */}
            {filteredAppointments.map((appointment) => {
              const style = getAppointmentStyle(appointment);
              return (
                <TouchableOpacity
                  key={appointment.id}
                  style={{
                    position: "absolute",
                    left: 64,
                    right: 0,
                    top: style.top,
                    height: style.height,
                    backgroundColor: style.backgroundColor,
                    opacity: style.opacity,
                    borderRadius: 4,
                    padding: 8,
                    margin: 2,
                  }}
                  onPress={() => onViewAppointment(appointment)}
                >
                  <Text className="font-bold">{appointment.clientName}</Text>
                  <Text>{appointment.service}</Text>
                  <Text className="text-xs">
                    {formatTime(appointment.time)} - {appointment.duration} min
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  // Render weekly view
  const renderWeeklyView = () => {
    const weekDates = generateWeekDates();
    const dayNames = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"];

    return (
      <ScrollView horizontal className="flex-1">
        <View className="flex-row">
          {weekDates.map((date, index) => {
            const dateStr = date.toISOString().split("T")[0];
            const dayAppointments = appointments.filter((appointment) => {
              const appointmentDate = appointment.date;
              const stylistMatch = selectedStylist
                ? appointment.stylist === selectedStylist
                : true;
              return appointmentDate === dateStr && stylistMatch;
            });

            const isToday = new Date().toISOString().split("T")[0] === dateStr;
            const isSelected =
              selectedDate.toISOString().split("T")[0] === dateStr;

            return (
              <View key={dateStr} className="w-40 border-r border-gray-200">
                <TouchableOpacity
                  className={`p-2 items-center ${isToday ? "bg-blue-100" : ""} ${isSelected ? "bg-blue-200" : ""}`}
                  onPress={() => setSelectedDate(date)}
                >
                  <Text className="font-bold">{dayNames[index]}</Text>
                  <Text>{date.getDate()}</Text>
                </TouchableOpacity>

                <ScrollView className="max-h-96">
                  {dayAppointments.map((appointment) => (
                    <TouchableOpacity
                      key={appointment.id}
                      className={`m-1 p-2 rounded-md ${appointment.status === "cancelled" ? "opacity-50" : ""}`}
                      style={{ backgroundColor: appointment.color }}
                      onPress={() => onViewAppointment(appointment)}
                    >
                      <Text className="font-bold">
                        {appointment.clientName}
                      </Text>
                      <Text className="text-xs">
                        {formatTime(appointment.time)}
                      </Text>
                      <Text className="text-xs">{appointment.service}</Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            );
          })}
        </View>
      </ScrollView>
    );
  };

  // Render monthly view
  const renderMonthlyView = () => {
    const monthDates = generateMonthDates();
    const dayNames = ["Dom", "Lun", "Mar", "Mié", "Jue", "Vie", "Sáb"];

    return (
      <View className="flex-1">
        <View className="flex-row">
          {dayNames.map((day) => (
            <View
              key={day}
              className="flex-1 items-center p-2 border-b border-gray-200"
            >
              <Text className="font-bold">{day}</Text>
            </View>
          ))}
        </View>

        <View className="flex-row flex-wrap">
          {monthDates.map((date) => {
            const dateStr = date.toISOString().split("T")[0];
            const dayAppointments = appointments.filter((appointment) => {
              const appointmentDate = appointment.date;
              const stylistMatch = selectedStylist
                ? appointment.stylist === selectedStylist
                : true;
              return appointmentDate === dateStr && stylistMatch;
            });

            const isCurrentMonth = date.getMonth() === selectedDate.getMonth();
            const isToday = new Date().toISOString().split("T")[0] === dateStr;
            const isSelected =
              selectedDate.toISOString().split("T")[0] === dateStr;

            return (
              <TouchableOpacity
                key={dateStr}
                className={`w-1/7 aspect-square p-1 border-b border-r border-gray-200 ${!isCurrentMonth ? "bg-gray-100" : ""} ${isToday ? "bg-blue-100" : ""} ${isSelected ? "bg-blue-200" : ""}`}
                onPress={() => setSelectedDate(date)}
              >
                <Text
                  className={`text-xs ${!isCurrentMonth ? "text-gray-400" : ""}`}
                >
                  {date.getDate()}
                </Text>

                {dayAppointments.length > 0 && (
                  <View className="flex-1 justify-end">
                    {dayAppointments.slice(0, 3).map((appointment) => (
                      <View
                        key={appointment.id}
                        className="h-1.5 my-0.5 rounded-full"
                        style={{ backgroundColor: appointment.color }}
                      />
                    ))}
                    {dayAppointments.length > 3 && (
                      <Text className="text-xs text-center">
                        +{dayAppointments.length - 3}
                      </Text>
                    )}
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  // Render appointment list for the selected date
  const renderAppointmentList = () => {
    const filteredAppointments = getFilteredAppointments();

    return (
      <View className="bg-white rounded-lg shadow-md p-4 mt-4">
        <Text className="text-lg font-bold mb-2">
          Citas para{" "}
          {selectedDate.toLocaleDateString("es-ES", {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </Text>

        {filteredAppointments.length === 0 ? (
          <Text className="text-gray-500 italic">
            No hay citas programadas para este día
          </Text>
        ) : (
          <FlatList
            data={filteredAppointments}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity
                className={`flex-row items-center p-3 mb-2 rounded-md ${item.status === "cancelled" ? "opacity-50" : ""}`}
                style={{ backgroundColor: item.color }}
                onPress={() => onViewAppointment(item)}
              >
                <View className="flex-1">
                  <Text className="font-bold">{item.clientName}</Text>
                  <Text>{item.service}</Text>
                  <Text className="text-xs">
                    {formatTime(item.time)} - {item.duration} min
                  </Text>
                  <Text className="text-xs">Estilista: {item.stylist}</Text>
                </View>

                <View className="flex-row">
                  <TouchableOpacity
                    className="bg-blue-500 p-2 rounded-full mr-2"
                    onPress={() => onReschedule(item)}
                  >
                    <Calendar size={16} color="white" />
                  </TouchableOpacity>

                  <TouchableOpacity
                    className="bg-red-500 p-2 rounded-full"
                    onPress={() => onCancel(item)}
                  >
                    <X size={16} color="white" />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            )}
          />
        )}
      </View>
    );
  };

  return (
    <View className="flex-1 bg-white p-4">
      {/* Header */}
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-2xl font-bold">Calendario</Text>

        <TouchableOpacity
          className="bg-blue-500 p-2 rounded-full"
          onPress={() => {
            setNewAppointment({
              clientName: "",
              service: "",
              date: selectedDate.toISOString().split("T")[0],
              time: "10:00",
              duration: 60,
              stylist: stylists[0],
              status: "confirmed",
            });
            setShowCreateModal(true);
            onCreateAppointment();
          }}
        >
          <Plus size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* View selector */}
      <View className="flex-row bg-gray-100 rounded-lg mb-4">
        <TouchableOpacity
          className={`flex-1 py-2 items-center ${calendarView === "daily" ? "bg-blue-500 rounded-lg" : ""}`}
          onPress={() => setCalendarView("daily")}
        >
          <Text
            className={
              calendarView === "daily" ? "text-white" : "text-gray-700"
            }
          >
            Diario
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          className={`flex-1 py-2 items-center ${calendarView === "weekly" ? "bg-blue-500 rounded-lg" : ""}`}
          onPress={() => setCalendarView("weekly")}
        >
          <Text
            className={
              calendarView === "weekly" ? "text-white" : "text-gray-700"
            }
          >
            Semanal
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          className={`flex-1 py-2 items-center ${calendarView === "monthly" ? "bg-blue-500 rounded-lg" : ""}`}
          onPress={() => setCalendarView("monthly")}
        >
          <Text
            className={
              calendarView === "monthly" ? "text-white" : "text-gray-700"
            }
          >
            Mensual
          </Text>
        </TouchableOpacity>
      </View>

      {/* Date navigation */}
      <View className="flex-row items-center justify-between mb-4">
        <TouchableOpacity className="p-2" onPress={navigatePrevious}>
          <ChevronLeft size={24} color="#0369a1" />
        </TouchableOpacity>

        <Text className="text-lg font-semibold">
          {calendarView === "daily" &&
            selectedDate.toLocaleDateString("es-ES", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          {calendarView === "weekly" &&
            `Semana del ${generateWeekDates()[0].toLocaleDateString("es-ES", { day: "numeric", month: "short" })} al ${generateWeekDates()[6].toLocaleDateString("es-ES", { day: "numeric", month: "short", year: "numeric" })}`}
          {calendarView === "monthly" &&
            selectedDate.toLocaleDateString("es-ES", {
              month: "long",
              year: "numeric",
            })}
        </Text>

        <TouchableOpacity className="p-2" onPress={navigateNext}>
          <ChevronRight size={24} color="#0369a1" />
        </TouchableOpacity>
      </View>

      {/* Stylist filter */}
      <View className="flex-row items-center mb-4">
        <View className="flex-row items-center bg-gray-100 rounded-lg p-2 flex-1">
          <Filter size={16} color="#4b5563" />
          <Text className="ml-2">Estilista:</Text>
        </View>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          className="ml-2"
        >
          <TouchableOpacity
            className={`px-3 py-1 mr-2 rounded-full ${selectedStylist === null ? "bg-blue-500" : "bg-gray-200"}`}
            onPress={() => setSelectedStylist(null)}
          >
            <Text
              className={
                selectedStylist === null ? "text-white" : "text-gray-700"
              }
            >
              Todos
            </Text>
          </TouchableOpacity>

          {stylists.map((stylist) => (
            <TouchableOpacity
              key={stylist}
              className={`px-3 py-1 mr-2 rounded-full ${selectedStylist === stylist ? "bg-blue-500" : "bg-gray-200"}`}
              onPress={() => setSelectedStylist(stylist)}
            >
              <Text
                className={
                  selectedStylist === stylist ? "text-white" : "text-gray-700"
                }
              >
                {stylist}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Calendar view */}
      <View className="flex-1 border border-gray-200 rounded-lg overflow-hidden mb-4">
        {calendarView === "daily" && renderDailyView()}
        {calendarView === "weekly" && renderWeeklyView()}
        {calendarView === "monthly" && renderMonthlyView()}
      </View>

      {/* Appointment list */}
      {renderAppointmentList()}

      {/* Create Appointment Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View className="flex-1 bg-white p-4">
          <View className="flex-row items-center justify-between mb-6">
            <Text className="text-2xl font-bold">Nueva Cita</Text>
            <TouchableOpacity
              className="p-2"
              onPress={() => setShowCreateModal(false)}
            >
              <X size={24} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <ScrollView className="flex-1">
            {/* Client Name */}
            <View className="mb-4">
              <Text className="text-lg font-semibold mb-2">
                Nombre del Cliente
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3 text-base"
                placeholder="Ingresa el nombre del cliente"
                value={newAppointment.clientName}
                onChangeText={(text) =>
                  setNewAppointment({ ...newAppointment, clientName: text })
                }
              />
            </View>

            {/* Service Selection */}
            <View className="mb-4">
              <Text className="text-lg font-semibold mb-2">Servicio</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {services.map((service) => (
                  <TouchableOpacity
                    key={service.name}
                    className={`mr-2 p-3 rounded-lg border ${
                      newAppointment.service === service.name
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-300 bg-white"
                    }`}
                    onPress={() =>
                      setNewAppointment({
                        ...newAppointment,
                        service: service.name,
                        duration: service.duration,
                      })
                    }
                  >
                    <Text
                      className={`text-center ${
                        newAppointment.service === service.name
                          ? "text-blue-700 font-semibold"
                          : "text-gray-700"
                      }`}
                    >
                      {service.name}
                    </Text>
                    <Text className="text-xs text-gray-500 text-center mt-1">
                      {service.duration} min
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Date */}
            <View className="mb-4">
              <Text className="text-lg font-semibold mb-2">Fecha</Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3 text-base"
                placeholder="YYYY-MM-DD"
                value={newAppointment.date}
                onChangeText={(text) =>
                  setNewAppointment({ ...newAppointment, date: text })
                }
              />
            </View>

            {/* Time */}
            <View className="mb-4">
              <Text className="text-lg font-semibold mb-2">Hora</Text>
              <View className="flex-row flex-wrap">
                {[
                  "09:00",
                  "09:30",
                  "10:00",
                  "10:30",
                  "11:00",
                  "11:30",
                  "12:00",
                  "12:30",
                  "13:00",
                  "13:30",
                  "14:00",
                  "14:30",
                  "15:00",
                  "15:30",
                  "16:00",
                  "16:30",
                  "17:00",
                  "17:30",
                ].map((time) => (
                  <TouchableOpacity
                    key={time}
                    className={`m-1 px-3 py-2 rounded-lg border ${
                      newAppointment.time === time
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-300 bg-white"
                    }`}
                    onPress={() =>
                      setNewAppointment({ ...newAppointment, time })
                    }
                  >
                    <Text
                      className={
                        newAppointment.time === time
                          ? "text-blue-700 font-semibold"
                          : "text-gray-700"
                      }
                    >
                      {time}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Stylist Selection */}
            <View className="mb-6">
              <Text className="text-lg font-semibold mb-2">Estilista</Text>
              <View className="flex-row flex-wrap">
                {stylists.map((stylist) => (
                  <TouchableOpacity
                    key={stylist}
                    className={`m-1 px-4 py-2 rounded-lg border ${
                      newAppointment.stylist === stylist
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-300 bg-white"
                    }`}
                    onPress={() =>
                      setNewAppointment({ ...newAppointment, stylist })
                    }
                  >
                    <Text
                      className={
                        newAppointment.stylist === stylist
                          ? "text-blue-700 font-semibold"
                          : "text-gray-700"
                      }
                    >
                      {stylist}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          {/* Save Button */}
          <TouchableOpacity
            className={`flex-row items-center justify-center p-4 rounded-lg ${
              newAppointment.clientName &&
              newAppointment.service &&
              newAppointment.date &&
              newAppointment.time &&
              newAppointment.stylist
                ? "bg-blue-500"
                : "bg-gray-300"
            }`}
            disabled={
              !newAppointment.clientName ||
              !newAppointment.service ||
              !newAppointment.date ||
              !newAppointment.time ||
              !newAppointment.stylist
            }
            onPress={async () => {
              try {
                const selectedService = services.find(
                  (s) => s.name === newAppointment.service,
                );

                const appointment: Appointment = {
                  id: Date.now().toString(),
                  clientName: newAppointment.clientName,
                  service: newAppointment.service,
                  date: newAppointment.date,
                  time: newAppointment.time,
                  duration: newAppointment.duration,
                  stylist: newAppointment.stylist,
                  status: newAppointment.status,
                  color: selectedService?.color || "#FFD1DC",
                };

                const updatedAppointments = [...appointments, appointment];
                await saveAppointments(updatedAppointments);
                setShowCreateModal(false);
                Alert.alert("Éxito", "Cita creada correctamente");
              } catch (error) {
                console.error("Error creating appointment:", error);
                Alert.alert(
                  "Error",
                  "No se pudo crear la cita. Inténtalo de nuevo.",
                );
              }
            }}
          >
            <Save size={20} color="white" className="mr-2" />
            <Text className="text-white font-semibold text-lg ml-2">
              Guardar Cita
            </Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
};

export default AppointmentCalendar;
