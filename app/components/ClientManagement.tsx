import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  ScrollView,
  Alert,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Image } from "expo-image";
import { useRouter } from "expo-router";
import {
  Search,
  Filter,
  User,
  Calendar,
  Scissors,
  Clock,
  ChevronRight,
  Star,
} from "lucide-react-native";
import ClientProfile from "./ClientProfile";

interface Client {
  id: string;
  name: string;
  lastVisit: string;
  serviceType: string;
  frequency: string;
  avatar: string;
  rating?: number;
}

interface ClientManagementProps {
  clients?: Client[];
  onClientSelect?: (client: Client) => void;
  onStartConsultation?: (client: Client) => void;
  onScheduleAppointment?: (client: Client) => void;
}

const DEFAULT_CLIENTS: Client[] = [
  {
    id: "1",
    name: "<PERSON>",
    lastVisit: "2 semanas",
    serviceType: "Coloración",
    frequency: "Mensual",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Maria",
    rating: 5,
  },
  {
    id: "2",
    name: "<PERSON>",
    lastVisit: "1 mes",
    serviceType: "Mechas",
    frequency: "Trimestral",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Carlos",
    rating: 4,
  },
  {
    id: "3",
    name: "Ana Martínez",
    lastVisit: "3 días",
    serviceType: "Balayage",
    frequency: "Bimestral",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Ana",
    rating: 5,
  },
  {
    id: "4",
    name: "Javier López",
    lastVisit: "2 meses",
    serviceType: "Coloración",
    frequency: "Mensual",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Javier",
    rating: 3,
  },
];

export default function ClientManagement({
  clients: propClients,
  onClientSelect = () => {},
  onStartConsultation = () => {},
  onScheduleAppointment = () => {},
}: ClientManagementProps) {
  const router = useRouter();
  const [clients, setClients] = useState<Client[]>(DEFAULT_CLIENTS);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilter, setActiveFilter] = useState("Todos");
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [showProfile, setShowProfile] = useState(false);

  useEffect(() => {
    loadClients();
  }, []);

  useEffect(() => {
    // Use prop clients if provided, otherwise use state clients
    if (propClients && propClients.length > 0) {
      setClients(propClients);
    }
  }, [propClients]);

  const loadClients = async () => {
    try {
      const savedClients = await AsyncStorage.getItem("salonier_clients");
      if (savedClients) {
        const parsedClients = JSON.parse(savedClients);
        setClients(parsedClients);
      }
    } catch (error) {
      console.error("Error loading clients:", error);
    }
  };

  const saveClients = async (newClients: Client[]) => {
    try {
      await AsyncStorage.setItem(
        "salonier_clients",
        JSON.stringify(newClients),
      );
      setClients(newClients);
    } catch (error) {
      console.error("Error saving clients:", error);
      Alert.alert("Error", "No se pudieron guardar los datos del cliente");
    }
  };

  const addClient = (newClient: Client) => {
    const updatedClients = [...clients, newClient];
    saveClients(updatedClients);
  };

  const updateClient = (updatedClient: Client) => {
    const updatedClients = clients.map((client) =>
      client.id === updatedClient.id ? updatedClient : client,
    );
    saveClients(updatedClients);
  };

  const deleteClient = (clientId: string) => {
    Alert.alert(
      "Eliminar Cliente",
      "¿Estás seguro de que quieres eliminar este cliente?",
      [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Eliminar",
          style: "destructive",
          onPress: () => {
            const updatedClients = clients.filter(
              (client) => client.id !== clientId,
            );
            saveClients(updatedClients);
          },
        },
      ],
    );
  };

  const filters = [
    "Todos",
    "Coloración",
    "Mechas",
    "Balayage",
    "Recientes",
    "Frecuentes",
  ];

  const filteredClients = clients.filter((client) => {
    // Search filter
    const matchesSearch = client.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());

    // Category filter
    let matchesFilter = true;
    if (activeFilter !== "Todos") {
      if (activeFilter === "Recientes") {
        matchesFilter =
          client.lastVisit.includes("día") ||
          client.lastVisit.includes("semana");
      } else if (activeFilter === "Frecuentes") {
        matchesFilter =
          client.frequency === "Mensual" || client.frequency === "Bimestral";
      } else {
        matchesFilter = client.serviceType === activeFilter;
      }
    }

    return matchesSearch && matchesFilter;
  });

  const renderClientItem = ({ item }: { item: Client }) => (
    <View className="bg-white rounded-xl p-4 mb-3 shadow-sm border border-gray-100">
      <View className="flex-row items-center">
        <Image
          source={{ uri: item.avatar }}
          className="w-16 h-16 rounded-full bg-gray-200"
          contentFit="cover"
        />
        <View className="flex-1 ml-4">
          <View className="flex-row justify-between items-center">
            <Text className="text-lg font-semibold">{item.name}</Text>
            <View className="flex-row items-center">
              {item.rating && (
                <View className="flex-row items-center mr-2">
                  <Star size={14} color="#FFD700" fill="#FFD700" />
                  <Text className="text-xs ml-1">{item.rating}</Text>
                </View>
              )}
              <ChevronRight size={18} color="#9CA3AF" />
            </View>
          </View>

          <View className="flex-row items-center mt-1">
            <Scissors size={12} color="#6B7280" className="mr-1" />
            <Text className="text-xs text-gray-500 mr-3">
              {item.serviceType}
            </Text>

            <Clock size={12} color="#6B7280" className="mr-1" />
            <Text className="text-xs text-gray-500">
              Última visita: {item.lastVisit}
            </Text>
          </View>

          <View className="flex-row mt-3">
            <TouchableOpacity
              className="bg-indigo-50 rounded-full px-3 py-1 mr-2"
              onPress={() => {
                setSelectedClient(item);
                setShowProfile(true);
                onClientSelect(item);
              }}
            >
              <Text className="text-xs text-indigo-700">Ver perfil</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="bg-purple-50 rounded-full px-3 py-1 mr-2"
              onPress={() => {
                // Si hay callback personalizado, usarlo; sino navegar directamente
                if (onStartConsultation.toString() !== "() => {}") {
                  onStartConsultation(item);
                } else {
                  router.push("/components/ColorConsultation");
                }
              }}
            >
              <Text className="text-xs text-purple-700">Iniciar consulta</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="bg-blue-50 rounded-full px-3 py-1"
              onPress={() => {
                // Si hay callback personalizado, usarlo; sino navegar directamente
                if (onScheduleAppointment.toString() !== "() => {}") {
                  onScheduleAppointment(item);
                } else {
                  router.push("/components/AppointmentCalendar");
                }
              }}
            >
              <Text className="text-xs text-blue-700">Agendar cita</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );

  // Convert Client to ClientData format for ClientProfile
  const convertToClientData = (client: Client) => {
    return {
      id: client.id,
      name: client.name,
      email: `${client.name.toLowerCase().replace(" ", ".")}@email.com`,
      phone: "+34 612 345 678",
      birthDate: "1985-03-15",
      avatar: client.avatar,
      joinDate: "2023-01-15",
      lastVisit: "2024-01-15",
      totalVisits: 12,
      rating: client.rating,
      notes: "Cliente satisfecho con los resultados.",
      hairHistory: {
        naturalLevel: 4,
        undertone: "Cálido",
        porosity: "Media",
        texture: "Ondulado",
        previousColors: ["Castaño Chocolate", "Mechas Caramelo"],
        allergies: ["PPD"],
        chemicalHistory: ["Alisado Brasileño (2022)"],
      },
      services: [
        {
          id: "1",
          date: "2024-01-15",
          service: client.serviceType,
          formula: "L'Oréal 7.3 + 20vol (1:1.5)",
          stylist: "Ana López",
          result: "Excelente",
          photos: [
            "https://images.unsplash.com/photo-1560869713-7d0b29837c64?w=400&q=80",
          ],
          notes: "Cliente muy contento con el resultado",
        },
      ],
    };
  };

  if (showProfile && selectedClient) {
    return (
      <ClientProfile
        client={convertToClientData(selectedClient)}
        onBack={() => {
          setShowProfile(false);
          setSelectedClient(null);
        }}
        onEdit={() => {}}
        onStartConsultation={() => {
          setShowProfile(false);
          setSelectedClient(null);
          // Si hay callback personalizado, usarlo; sino navegar directamente
          if (onStartConsultation.toString() !== "() => {}") {
            onStartConsultation(selectedClient);
          } else {
            router.push("/components/ColorConsultation");
          }
        }}
        onScheduleAppointment={() => {
          setShowProfile(false);
          setSelectedClient(null);
          // Si hay callback personalizado, usarlo; sino navegar directamente
          if (onScheduleAppointment.toString() !== "() => {}") {
            onScheduleAppointment(selectedClient);
          } else {
            router.push("/components/AppointmentCalendar");
          }
        }}
      />
    );
  }

  return (
    <View className="flex-1 bg-gray-50 p-4">
      {/* Search Bar */}
      <View className="flex-row items-center bg-white rounded-xl px-4 mb-4 border border-gray-200">
        <Search size={20} color="#9CA3AF" />
        <TextInput
          className="flex-1 py-3 px-2"
          placeholder="Buscar cliente..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Filters */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        className="mb-4"
      >
        <View className="flex-row">
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter}
              className={`mr-2 px-4 py-2 rounded-full flex-row items-center ${activeFilter === filter ? "bg-indigo-600" : "bg-white border border-gray-200"}`}
              onPress={() => setActiveFilter(filter)}
            >
              {filter === "Todos" && (
                <User
                  size={14}
                  color={activeFilter === filter ? "#FFFFFF" : "#6B7280"}
                  className="mr-1"
                />
              )}
              {filter === "Recientes" && (
                <Clock
                  size={14}
                  color={activeFilter === filter ? "#FFFFFF" : "#6B7280"}
                  className="mr-1"
                />
              )}
              {filter === "Frecuentes" && (
                <Calendar
                  size={14}
                  color={activeFilter === filter ? "#FFFFFF" : "#6B7280"}
                  className="mr-1"
                />
              )}
              <Text
                className={`text-sm ${activeFilter === filter ? "text-white" : "text-gray-600"}`}
              >
                {filter}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Client List */}
      {filteredClients.length > 0 ? (
        <FlatList
          data={filteredClients}
          renderItem={renderClientItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        />
      ) : (
        <View className="flex-1 justify-center items-center">
          <Text className="text-gray-500 text-center">
            No se encontraron clientes con estos criterios
          </Text>
        </View>
      )}

      {/* Add Client Button */}
      <TouchableOpacity
        className="absolute bottom-6 right-6 bg-indigo-600 w-14 h-14 rounded-full justify-center items-center shadow-lg"
        onPress={() => {}}
      >
        <Text className="text-white text-2xl font-semibold">+</Text>
      </TouchableOpacity>
    </View>
  );
}
