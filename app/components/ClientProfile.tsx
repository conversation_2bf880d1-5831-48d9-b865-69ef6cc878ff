import React, { useState } from "react";
import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import { Image } from "expo-image";
import {
  ArrowLeft,
  Edit3,
  Phone,
  Mail,
  Calendar,
  Clock,
  Star,
  FileText,
  AlertCircle,
  CheckCircle,
  User,
  Scissors,
} from "lucide-react-native";

interface HairService {
  id: string;
  date: string;
  service: string;
  formula?: string;
  stylist: string;
  result: string;
  photos?: string[];
  notes?: string;
}

interface HairHistory {
  naturalLevel: number;
  undertone: string;
  porosity: string;
  texture: string;
  previousColors: string[];
  allergies?: string[];
  chemicalHistory: string[];
}

interface ClientData {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  birthDate?: string;
  avatar: string;
  joinDate: string;
  lastVisit: string;
  totalVisits: number;
  rating?: number;
  notes?: string;
  hairHistory: HairHistory;
  services: HairService[];
}

interface ClientProfileProps {
  client?: ClientData;
  onBack?: () => void;
  onEdit?: (client: ClientData) => void;
  onStartConsultation?: (client: ClientData) => void;
  onScheduleAppointment?: (client: ClientData) => void;
}

export default function ClientProfile({
  client = {
    id: "1",
    name: "María García",
    email: "<EMAIL>",
    phone: "+34 612 345 678",
    birthDate: "1985-03-15",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Maria",
    joinDate: "2023-01-15",
    lastVisit: "2024-01-15",
    totalVisits: 12,
    rating: 5,
    notes: "Cliente muy satisfecha con los resultados. Prefiere tonos cálidos.",
    hairHistory: {
      naturalLevel: 4,
      undertone: "Cálido",
      porosity: "Media",
      texture: "Ondulado",
      previousColors: ["Castaño Chocolate", "Mechas Caramelo", "Balayage Miel"],
      allergies: ["PPD"],
      chemicalHistory: ["Alisado Brasileño (2022)", "Permanente (2020)"],
    },
    services: [
      {
        id: "1",
        date: "2024-01-15",
        service: "Balayage + Tratamiento",
        formula: "L'Oréal 7.3 + 20vol (1:1.5)",
        stylist: "Ana López",
        result: "Excelente",
        photos: [
          "https://images.unsplash.com/photo-1560869713-7d0b29837c64?w=400&q=80",
        ],
        notes: "Cliente muy contenta con el resultado",
      },
      {
        id: "2",
        date: "2023-12-10",
        service: "Color Global + Corte",
        formula: "Wella 6/7 + 30vol (1:2)",
        stylist: "Ana López",
        result: "Bueno",
        notes: "Necesitó retoque en raíces",
      },
      {
        id: "3",
        date: "2023-11-05",
        service: "Mechas + Peinado",
        formula: "Decolorante + Toner 9.1",
        stylist: "Carlos Ruiz",
        result: "Excelente",
      },
    ],
  },
  onBack = () => {},
  onEdit = () => {},
  onStartConsultation = () => {},
  onScheduleAppointment = () => {},
}: ClientProfileProps) {
  const [activeTab, setActiveTab] = useState("info");
  const [isEditing, setIsEditing] = useState(false);

  const tabs = [
    { id: "info", label: "Información", icon: User },
    { id: "hair", label: "Historial Capilar", icon: Scissors },
    { id: "services", label: "Servicios", icon: FileText },
  ];

  const renderPersonalInfo = () => (
    <View className="bg-white rounded-xl p-4 mb-4">
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-lg font-semibold">Información Personal</Text>
        <TouchableOpacity onPress={() => setIsEditing(!isEditing)}>
          <Edit3 size={18} color="#6B7280" />
        </TouchableOpacity>
      </View>

      <View className="space-y-3">
        <View className="flex-row items-center">
          <Mail size={16} color="#6B7280" className="mr-3" />
          <Text className="text-gray-600">
            {client.email || "No registrado"}
          </Text>
        </View>

        <View className="flex-row items-center">
          <Phone size={16} color="#6B7280" className="mr-3" />
          <Text className="text-gray-600">
            {client.phone || "No registrado"}
          </Text>
        </View>

        <View className="flex-row items-center">
          <Calendar size={16} color="#6B7280" className="mr-3" />
          <Text className="text-gray-600">
            Cliente desde: {new Date(client.joinDate).toLocaleDateString()}
          </Text>
        </View>

        <View className="flex-row items-center">
          <Clock size={16} color="#6B7280" className="mr-3" />
          <Text className="text-gray-600">
            Última visita: {new Date(client.lastVisit).toLocaleDateString()}
          </Text>
        </View>

        <View className="flex-row items-center">
          <Star size={16} color="#FFD700" fill="#FFD700" className="mr-3" />
          <Text className="text-gray-600">
            {client.totalVisits} visitas • Valoración: {client.rating}/5
          </Text>
        </View>
      </View>

      {client.notes && (
        <View className="mt-4 p-3 bg-blue-50 rounded-lg">
          <Text className="text-sm text-blue-800">
            <Text className="font-semibold">Notas: </Text>
            {client.notes}
          </Text>
        </View>
      )}
    </View>
  );

  const renderHairHistory = () => (
    <View className="space-y-4">
      {/* Hair Analysis */}
      <View className="bg-white rounded-xl p-4">
        <Text className="text-lg font-semibold mb-4">Análisis Capilar</Text>
        <View className="flex-row flex-wrap justify-between">
          <View className="bg-gray-50 p-3 rounded-lg w-[48%] mb-2">
            <Text className="text-xs text-gray-500 uppercase tracking-wide">
              Nivel Natural
            </Text>
            <Text className="text-lg font-semibold">
              {client.hairHistory.naturalLevel}
            </Text>
          </View>
          <View className="bg-gray-50 p-3 rounded-lg w-[48%] mb-2">
            <Text className="text-xs text-gray-500 uppercase tracking-wide">
              Subtono
            </Text>
            <Text className="text-lg font-semibold">
              {client.hairHistory.undertone}
            </Text>
          </View>
          <View className="bg-gray-50 p-3 rounded-lg w-[48%] mb-2">
            <Text className="text-xs text-gray-500 uppercase tracking-wide">
              Porosidad
            </Text>
            <Text className="text-lg font-semibold">
              {client.hairHistory.porosity}
            </Text>
          </View>
          <View className="bg-gray-50 p-3 rounded-lg w-[48%] mb-2">
            <Text className="text-xs text-gray-500 uppercase tracking-wide">
              Textura
            </Text>
            <Text className="text-lg font-semibold">
              {client.hairHistory.texture}
            </Text>
          </View>
        </View>
      </View>

      {/* Previous Colors */}
      <View className="bg-white rounded-xl p-4">
        <Text className="text-lg font-semibold mb-3">Colores Anteriores</Text>
        <View className="flex-row flex-wrap">
          {client.hairHistory.previousColors.map((color, index) => (
            <View
              key={index}
              className="bg-purple-100 px-3 py-1 rounded-full mr-2 mb-2"
            >
              <Text className="text-purple-700 text-sm">{color}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Chemical History */}
      <View className="bg-white rounded-xl p-4">
        <Text className="text-lg font-semibold mb-3">Historial Químico</Text>
        {client.hairHistory.chemicalHistory.map((treatment, index) => (
          <View key={index} className="flex-row items-center mb-2">
            <CheckCircle size={16} color="#10B981" className="mr-2" />
            <Text className="text-gray-700">{treatment}</Text>
          </View>
        ))}
      </View>

      {/* Allergies */}
      {client.hairHistory.allergies &&
        client.hairHistory.allergies.length > 0 && (
          <View className="bg-red-50 rounded-xl p-4 border border-red-200">
            <View className="flex-row items-center mb-2">
              <AlertCircle size={18} color="#EF4444" className="mr-2" />
              <Text className="text-lg font-semibold text-red-800">
                Alergias
              </Text>
            </View>
            {client.hairHistory.allergies.map((allergy, index) => (
              <Text key={index} className="text-red-700 ml-6">
                • {allergy}
              </Text>
            ))}
          </View>
        )}
    </View>
  );

  const renderServices = () => (
    <View className="space-y-4">
      {client.services.map((service) => (
        <View key={service.id} className="bg-white rounded-xl p-4">
          <View className="flex-row justify-between items-start mb-3">
            <View className="flex-1">
              <Text className="text-lg font-semibold">{service.service}</Text>
              <Text className="text-gray-500">
                {new Date(service.date).toLocaleDateString()}
              </Text>
            </View>
            <View
              className={`px-3 py-1 rounded-full ${
                service.result === "Excelente"
                  ? "bg-green-100"
                  : service.result === "Bueno"
                    ? "bg-yellow-100"
                    : "bg-red-100"
              }`}
            >
              <Text
                className={`text-sm ${
                  service.result === "Excelente"
                    ? "text-green-700"
                    : service.result === "Bueno"
                      ? "text-yellow-700"
                      : "text-red-700"
                }`}
              >
                {service.result}
              </Text>
            </View>
          </View>

          {service.formula && (
            <View className="bg-gray-50 p-3 rounded-lg mb-3">
              <Text className="text-xs text-gray-500 uppercase tracking-wide mb-1">
                Fórmula
              </Text>
              <Text className="text-gray-800">{service.formula}</Text>
            </View>
          )}

          <View className="flex-row items-center mb-2">
            <User size={14} color="#6B7280" className="mr-2" />
            <Text className="text-gray-600">Estilista: {service.stylist}</Text>
          </View>

          {service.notes && (
            <View className="bg-blue-50 p-3 rounded-lg mb-3">
              <Text className="text-blue-800 text-sm">{service.notes}</Text>
            </View>
          )}

          {service.photos && service.photos.length > 0 && (
            <View className="flex-row">
              {service.photos.map((photo, index) => (
                <Image
                  key={index}
                  source={{ uri: photo }}
                  className="w-20 h-20 rounded-lg mr-2"
                  contentFit="cover"
                />
              ))}
            </View>
          )}
        </View>
      ))}
    </View>
  );

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white px-4 py-3 border-b border-gray-200">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={onBack} className="flex-row items-center">
            <ArrowLeft size={24} color="#374151" />
            <Text className="ml-2 text-lg font-semibold">
              Perfil del Cliente
            </Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => onEdit(client)}>
            <Edit3 size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Client Header */}
      <View className="bg-white p-4 border-b border-gray-200">
        <View className="flex-row items-center">
          <Image
            source={{ uri: client.avatar }}
            className="w-20 h-20 rounded-full bg-gray-200"
            contentFit="cover"
          />
          <View className="flex-1 ml-4">
            <Text className="text-2xl font-bold">{client.name}</Text>
            <View className="flex-row items-center mt-1">
              {client.rating && (
                <View className="flex-row items-center mr-3">
                  <Star size={16} color="#FFD700" fill="#FFD700" />
                  <Text className="ml-1 text-gray-600">{client.rating}/5</Text>
                </View>
              )}
              <Text className="text-gray-500">
                {client.totalVisits} visitas
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View className="flex-row mt-4 gap-3">
          <TouchableOpacity
            className="flex-1 bg-indigo-600 rounded-lg py-3 items-center"
            onPress={() => onStartConsultation(client)}
          >
            <Text className="text-white font-semibold">Iniciar Consulta</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="flex-1 bg-blue-600 rounded-lg py-3 items-center"
            onPress={() => onScheduleAppointment(client)}
          >
            <Text className="text-white font-semibold">Agendar Cita</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Tabs */}
      <View className="bg-white border-b border-gray-200">
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row px-4">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <TouchableOpacity
                  key={tab.id}
                  className={`flex-row items-center px-4 py-3 mr-2 ${
                    activeTab === tab.id ? "border-b-2 border-indigo-600" : ""
                  }`}
                  onPress={() => setActiveTab(tab.id)}
                >
                  <IconComponent
                    size={16}
                    color={activeTab === tab.id ? "#4F46E5" : "#6B7280"}
                    className="mr-2"
                  />
                  <Text
                    className={`${
                      activeTab === tab.id
                        ? "text-indigo-600 font-semibold"
                        : "text-gray-600"
                    }`}
                  >
                    {tab.label}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        {activeTab === "info" && renderPersonalInfo()}
        {activeTab === "hair" && renderHairHistory()}
        {activeTab === "services" && renderServices()}
      </ScrollView>
    </View>
  );
}
