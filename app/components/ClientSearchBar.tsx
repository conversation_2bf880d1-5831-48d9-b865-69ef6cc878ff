import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Keyboard,
} from "react-native";
import {
  Search,
  User,
  Clock,
  Star,
  Plus,
  X,
} from "lucide-react-native";

interface Client {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  avatar: string;
  lastService: string;
  lastVisit: string;
  isFrequent: boolean;
  totalVisits: number;
  preferredServices: string[];
  notes?: string;
}

interface ClientSearchBarProps {
  onSelect: (client: Client) => void;
  onCreateNew?: (name: string) => void;
  placeholder?: string;
  showFrequentFirst?: boolean;
  maxResults?: number;
}

// Mock data expandida para el prototipo
const mockClientsDatabase: Client[] = [
  {
    id: "1",
    name: "<PERSON>",
    phone: "+34 666 123 456",
    email: "<EMAIL>",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Ana",
    lastService: "Mechas rubias",
    lastVisit: "hace 6 semanas",
    isFrequent: true,
    totalVisits: 12,
    preferredServices: ["Mechas", "Corte", "Tratamiento"],
    notes: "Prefiere tonos cálidos, cabello sensible"
  },
  {
    id: "2",
    name: "María López",
    phone: "+34 666 234 567",
    email: "<EMAIL>",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Maria",
    lastService: "Cobertura canas",
    lastVisit: "hace 4 semanas",
    isFrequent: true,
    totalVisits: 8,
    preferredServices: ["Coloración", "Corte"],
    notes: "Viene cada 4-5 semanas, muy puntual"
  },
  {
    id: "3",
    name: "Carmen Ruiz",
    phone: "+34 666 345 678",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Carmen",
    lastService: "Cambio de look",
    lastVisit: "hace 2 meses",
    isFrequent: false,
    totalVisits: 3,
    preferredServices: ["Coloración", "Peinado"],
  },
  {
    id: "4",
    name: "Laura Martín",
    phone: "+34 666 456 789",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Laura",
    lastService: "Reflejos",
    lastVisit: "hace 3 semanas",
    isFrequent: true,
    totalVisits: 15,
    preferredServices: ["Mechas", "Reflejos", "Tratamiento"],
    notes: "Cliente VIP, siempre reserva con antelación"
  },
  {
    id: "5",
    name: "Isabel Fernández",
    phone: "+34 666 567 890",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Isabel",
    lastService: "Corte y color",
    lastVisit: "hace 1 semana",
    isFrequent: false,
    totalVisits: 2,
    preferredServices: ["Corte", "Coloración"],
  },
  {
    id: "6",
    name: "Pilar González",
    phone: "+34 666 678 901",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Pilar",
    lastService: "Tratamiento keratina",
    lastVisit: "hace 5 semanas",
    isFrequent: true,
    totalVisits: 10,
    preferredServices: ["Tratamiento", "Corte"],
    notes: "Cabello rizado, prefiere productos naturales"
  }
];

const ClientSearchBar: React.FC<ClientSearchBarProps> = ({
  onSelect,
  onCreateNew,
  placeholder = "Buscar cliente...",
  showFrequentFirst = true,
  maxResults = 6,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    if (searchQuery.trim().length === 0) {
      // Mostrar clientes frecuentes por defecto
      if (showFrequentFirst) {
        const frequentClients = mockClientsDatabase
          .filter(client => client.isFrequent)
          .sort((a, b) => b.totalVisits - a.totalVisits)
          .slice(0, maxResults);
        setFilteredClients(frequentClients);
      } else {
        setFilteredClients([]);
      }
      setShowResults(false);
      return;
    }

    setIsSearching(true);
    setShowResults(true);

    // Simular búsqueda con delay realista
    const searchTimeout = setTimeout(() => {
      const query = searchQuery.toLowerCase().trim();
      
      let results = mockClientsDatabase.filter(client => 
        client.name.toLowerCase().includes(query) ||
        client.phone?.includes(query) ||
        client.email?.toLowerCase().includes(query) ||
        client.lastService.toLowerCase().includes(query)
      );

      // Ordenar resultados: frecuentes primero, luego por relevancia
      results.sort((a, b) => {
        if (a.isFrequent && !b.isFrequent) return -1;
        if (!a.isFrequent && b.isFrequent) return 1;
        
        // Si ambos son frecuentes o no frecuentes, ordenar por número de visitas
        return b.totalVisits - a.totalVisits;
      });

      setFilteredClients(results.slice(0, maxResults));
      setIsSearching(false);
    }, 300);

    return () => clearTimeout(searchTimeout);
  }, [searchQuery, showFrequentFirst, maxResults]);

  const handleClientSelect = (client: Client) => {
    setSearchQuery("");
    setShowResults(false);
    Keyboard.dismiss();
    onSelect(client);
  };

  const handleCreateNew = () => {
    if (onCreateNew && searchQuery.trim()) {
      onCreateNew(searchQuery.trim());
      setSearchQuery("");
      setShowResults(false);
      Keyboard.dismiss();
    }
  };

  const clearSearch = () => {
    setSearchQuery("");
    setShowResults(false);
    Keyboard.dismiss();
  };

  const renderClientItem = ({ item: client }: { item: Client }) => (
    <TouchableOpacity
      className="bg-white p-4 border-b border-gray-100 flex-row items-center"
      onPress={() => handleClientSelect(client)}
    >
      <View className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center mr-3">
        <User size={24} color="#3B82F6" />
      </View>
      
      <View className="flex-1">
        <View className="flex-row items-center mb-1">
          <Text className="font-semibold text-gray-800 flex-1">{client.name}</Text>
          {client.isFrequent && (
            <View className="flex-row items-center bg-yellow-100 px-2 py-1 rounded-full ml-2">
              <Star size={12} color="#F59E0B" />
              <Text className="text-xs text-yellow-800 font-medium ml-1">VIP</Text>
            </View>
          )}
        </View>
        
        <View className="flex-row items-center">
          <Clock size={12} color="#6B7280" />
          <Text className="text-sm text-gray-600 ml-1">
            {client.lastService} • {client.lastVisit}
          </Text>
        </View>
        
        {client.phone && (
          <Text className="text-xs text-gray-500 mt-1">{client.phone}</Text>
        )}
        
        <View className="flex-row items-center mt-1">
          <Text className="text-xs text-gray-500">
            {client.totalVisits} visitas
          </Text>
          {client.preferredServices.length > 0 && (
            <Text className="text-xs text-gray-500 ml-2">
              • {client.preferredServices.slice(0, 2).join(", ")}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View className="relative">
      {/* Barra de búsqueda */}
      <View className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <View className="flex-row items-center px-4 py-3">
          <Search size={20} color="#6B7280" />
          <TextInput
            className="flex-1 ml-3 text-gray-800 text-base"
            placeholder={placeholder}
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={setSearchQuery}
            onFocus={() => setShowResults(true)}
            autoCapitalize="words"
            autoCorrect={false}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={clearSearch} className="ml-2">
              <X size={20} color="#6B7280" />
            </TouchableOpacity>
          )}
        </View>

        {/* Resultados de búsqueda */}
        {(showResults || (searchQuery.length === 0 && showFrequentFirst)) && (
          <View className="border-t border-gray-100">
            {isSearching ? (
              <View className="p-4 items-center">
                <View className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                <Text className="text-gray-600 text-sm mt-2">Buscando...</Text>
              </View>
            ) : filteredClients.length > 0 ? (
              <>
                {searchQuery.length === 0 && showFrequentFirst && (
                  <View className="px-4 py-2 bg-gray-50 border-b border-gray-100">
                    <Text className="text-sm font-medium text-gray-700">
                      ⭐ Clientes Frecuentes
                    </Text>
                  </View>
                )}
                <FlatList
                  data={filteredClients}
                  renderItem={renderClientItem}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                  showsVerticalScrollIndicator={false}
                />
              </>
            ) : searchQuery.length > 0 ? (
              <View className="p-4">
                <Text className="text-gray-600 text-center mb-3">
                  No se encontraron clientes con "{searchQuery}"
                </Text>
                {onCreateNew && (
                  <TouchableOpacity
                    className="bg-blue-500 p-3 rounded-lg flex-row items-center justify-center"
                    onPress={handleCreateNew}
                  >
                    <Plus size={20} color="white" />
                    <Text className="text-white font-semibold ml-2">
                      Crear cliente "{searchQuery}"
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : null}
          </View>
        )}
      </View>

      {/* Sugerencias rápidas */}
      {searchQuery.length === 0 && !showResults && (
        <View className="mt-3">
          <Text className="text-sm text-gray-600 mb-2">Búsqueda rápida:</Text>
          <View className="flex-row flex-wrap">
            <TouchableOpacity 
              className="bg-gray-100 px-3 py-1 rounded-full mr-2 mb-2"
              onPress={() => setSearchQuery("Ana")}
            >
              <Text className="text-gray-700 text-sm">Ana</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              className="bg-gray-100 px-3 py-1 rounded-full mr-2 mb-2"
              onPress={() => setSearchQuery("María")}
            >
              <Text className="text-gray-700 text-sm">María</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              className="bg-gray-100 px-3 py-1 rounded-full mr-2 mb-2"
              onPress={() => setSearchQuery("666")}
            >
              <Text className="text-gray-700 text-sm">Por teléfono</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default ClientSearchBar;
