import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
} from "react-native";
import {
  Palette,
  Camera,
  Target,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Clock,
  Shield,
} from "lucide-react-native";

interface ColorAnalysisData {
  currentColor: {
    level: number | null;
    undertone: string | null;
    reflections: string[];
  };
  desiredColor: {
    level: number | null;
    undertone: string | null;
    technique: string | null;
    referenceImages: string[];
    description: string | null;
  };
  viabilityAnalysis: {
    sessionsRequired: number;
    riskLevel: string;
    recommendations: string[];
    lightening: number;
    darkening: number;
  };
}

interface ColorAnalysisPhaseProps {
  data: ColorAnalysisData;
  onUpdate: (data: ColorAnalysisData) => void;
  onComplete: () => void;
  diagnosisData: any; // Datos del diagnóstico previo
}

const ColorAnalysisPhase: React.FC<ColorAnalysisPhaseProps> = ({
  data,
  onUpdate,
  onComplete,
  diagnosisData,
}) => {
  const [activeTab, setActiveTab] = useState<'current' | 'desired' | 'viability'>('current');

  // Datos de referencia profesional
  const colorLevels = [
    { level: 1, name: "Negro", hex: "#1a1a1a" },
    { level: 2, name: "Castaño Muy Oscuro", hex: "#2d1b14" },
    { level: 3, name: "Castaño Oscuro", hex: "#3d2817" },
    { level: 4, name: "Castaño Medio", hex: "#4a3728" },
    { level: 5, name: "Castaño Claro", hex: "#6b4423" },
    { level: 6, name: "Rubio Oscuro", hex: "#8b5a2b" },
    { level: 7, name: "Rubio Medio", hex: "#a67c52" },
    { level: 8, name: "Rubio Claro", hex: "#c19a6b" },
    { level: 9, name: "Rubio Muy Claro", hex: "#d4b896" },
    { level: 10, name: "Rubio Extra Claro", hex: "#e8d5c4" },
  ];

  const undertones = [
    { id: 'neutral', name: 'Neutro', color: '#8B7355' },
    { id: 'ash', name: 'Ceniza', color: '#7A8B8B' },
    { id: 'golden', name: 'Dorado', color: '#DAA520' },
    { id: 'copper', name: 'Cobrizo', color: '#B87333' },
    { id: 'red', name: 'Rojizo', color: '#CD5C5C' },
    { id: 'violet', name: 'Violeta', color: '#8B7D6B' },
  ];

  const techniques = [
    { id: 'global', name: 'Color Global', description: 'Aplicación en todo el cabello' },
    { id: 'highlights', name: 'Mechas/Highlights', description: 'Aclaración selectiva' },
    { id: 'balayage', name: 'Balayage', description: 'Técnica a mano alzada' },
    { id: 'ombre', name: 'Ombré', description: 'Degradado de color' },
    { id: 'babylights', name: 'Babylights', description: 'Mechas muy finas' },
    { id: 'foilyage', name: 'Foilyage', description: 'Balayage con papel' },
  ];

  const updateCurrentColor = (field: string, value: any) => {
    onUpdate({
      ...data,
      currentColor: {
        ...data.currentColor,
        [field]: value,
      },
    });
  };

  const updateDesiredColor = (field: string, value: any) => {
    onUpdate({
      ...data,
      desiredColor: {
        ...data.desiredColor,
        [field]: value,
      },
    });
  };

  const calculateViability = () => {
    if (!data.currentColor.level || !data.desiredColor.level) return;

    const levelDifference = data.desiredColor.level - data.currentColor.level;
    const lightening = levelDifference > 0 ? levelDifference : 0;
    const darkening = levelDifference < 0 ? Math.abs(levelDifference) : 0;

    let sessionsRequired = 1;
    let riskLevel = 'low';
    let recommendations: string[] = [];

    // Análisis de aclaración
    if (lightening > 0) {
      if (lightening <= 2) {
        sessionsRequired = 1;
        riskLevel = 'low';
        recommendations.push('Aclaración segura en una sesión');
      } else if (lightening <= 4) {
        sessionsRequired = lightening > 3 ? 2 : 1;
        riskLevel = 'medium';
        recommendations.push('Considerar pre-aclaración');
        recommendations.push('Usar tratamientos protectores');
      } else {
        sessionsRequired = Math.ceil(lightening / 2);
        riskLevel = 'high';
        recommendations.push('Proceso gradual obligatorio');
        recommendations.push('Evaluación de elasticidad necesaria');
        recommendations.push('Tratamientos reconstructivos entre sesiones');
      }
    }

    // Consideraciones adicionales basadas en historial químico
    if (diagnosisData?.chemicalHistory?.length > 2) {
      riskLevel = riskLevel === 'low' ? 'medium' : 'high';
      recommendations.push('Cabello previamente tratado - mayor precaución');
    }

    // Consideraciones de porosidad
    if (diagnosisData?.porosity === 'high') {
      recommendations.push('Porosidad alta - usar productos de baja alcalinidad');
    }

    onUpdate({
      ...data,
      viabilityAnalysis: {
        sessionsRequired,
        riskLevel,
        recommendations,
        lightening,
        darkening,
      },
    });
  };

  const renderCurrentColorAnalysis = () => (
    <View className="p-4">
      <Text className="text-xl font-bold text-gray-800 mb-4">
        Color Actual del Cabello
      </Text>

      <View className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
        <Text className="text-blue-800 font-semibold mb-2">
          Datos del Diagnóstico:
        </Text>
        <Text className="text-blue-700">
          • Nivel base natural: {diagnosisData?.naturalLevel || 'No definido'}{'\n'}
          • Subtono natural: {diagnosisData?.undertone || 'No definido'}{'\n'}
          • Tratamientos previos: {diagnosisData?.chemicalHistory?.length || 0}
        </Text>
      </View>

      <Text className="text-lg font-semibold text-gray-800 mb-3">
        Nivel de Color Actual
      </Text>
      <View className="flex-row flex-wrap justify-between mb-6">
        {colorLevels.map((level) => (
          <TouchableOpacity
            key={level.level}
            onPress={() => updateCurrentColor('level', level.level)}
            className={`w-[18%] aspect-square rounded-lg border-2 mb-2 items-center justify-center ${
              data.currentColor.level === level.level
                ? 'border-blue-500'
                : 'border-gray-300'
            }`}
            style={{ backgroundColor: level.hex }}
          >
            <Text className="text-white text-xs font-bold">{level.level}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text className="text-lg font-semibold text-gray-800 mb-3">
        Subtono Actual
      </Text>
      <View className="flex-row flex-wrap justify-between">
        {undertones.map((tone) => (
          <TouchableOpacity
            key={tone.id}
            onPress={() => updateCurrentColor('undertone', tone.id)}
            className={`w-[30%] p-3 rounded-lg border mb-2 items-center ${
              data.currentColor.undertone === tone.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-300 bg-white'
            }`}
          >
            <View
              className="w-6 h-6 rounded-full mb-1"
              style={{ backgroundColor: tone.color }}
            />
            <Text className="text-xs text-gray-700 text-center">{tone.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderDesiredColorDefinition = () => (
    <View className="p-4">
      <Text className="text-xl font-bold text-gray-800 mb-4">
        Color Deseado
      </Text>

      <Text className="text-lg font-semibold text-gray-800 mb-3">
        Nivel Objetivo
      </Text>
      <View className="flex-row flex-wrap justify-between mb-6">
        {colorLevels.map((level) => (
          <TouchableOpacity
            key={level.level}
            onPress={() => updateDesiredColor('level', level.level)}
            className={`w-[18%] aspect-square rounded-lg border-2 mb-2 items-center justify-center ${
              data.desiredColor.level === level.level
                ? 'border-purple-500'
                : 'border-gray-300'
            }`}
            style={{ backgroundColor: level.hex }}
          >
            <Text className="text-white text-xs font-bold">{level.level}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text className="text-lg font-semibold text-gray-800 mb-3">
        Subtono Deseado
      </Text>
      <View className="flex-row flex-wrap justify-between mb-6">
        {undertones.map((tone) => (
          <TouchableOpacity
            key={tone.id}
            onPress={() => updateDesiredColor('undertone', tone.id)}
            className={`w-[30%] p-3 rounded-lg border mb-2 items-center ${
              data.desiredColor.undertone === tone.id
                ? 'border-purple-500 bg-purple-50'
                : 'border-gray-300 bg-white'
            }`}
          >
            <View
              className="w-6 h-6 rounded-full mb-1"
              style={{ backgroundColor: tone.color }}
            />
            <Text className="text-xs text-gray-700 text-center">{tone.name}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text className="text-lg font-semibold text-gray-800 mb-3">
        Técnica Recomendada
      </Text>
      <View className="space-y-2">
        {techniques.map((technique) => (
          <TouchableOpacity
            key={technique.id}
            onPress={() => updateDesiredColor('technique', technique.id)}
            className={`p-3 rounded-lg border ${
              data.desiredColor.technique === technique.id
                ? 'border-purple-500 bg-purple-50'
                : 'border-gray-300 bg-white'
            }`}
          >
            <Text className="font-semibold text-gray-800">{technique.name}</Text>
            <Text className="text-sm text-gray-600">{technique.description}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const isComplete = () => {
    return (
      data.currentColor.level !== null &&
      data.currentColor.undertone !== null &&
      data.desiredColor.level !== null &&
      data.desiredColor.undertone !== null &&
      data.desiredColor.technique !== null
    );
  };

  return (
    <View className="flex-1">
      {/* Tabs */}
      <View className="flex-row bg-white border-b border-gray-200">
        <TouchableOpacity
          onPress={() => setActiveTab('current')}
          className={`flex-1 p-4 ${activeTab === 'current' ? 'border-b-2 border-blue-500' : ''}`}
        >
          <Text className={`text-center font-medium ${
            activeTab === 'current' ? 'text-blue-600' : 'text-gray-600'
          }`}>
            Color Actual
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => setActiveTab('desired')}
          className={`flex-1 p-4 ${activeTab === 'desired' ? 'border-b-2 border-purple-500' : ''}`}
        >
          <Text className={`text-center font-medium ${
            activeTab === 'desired' ? 'text-purple-600' : 'text-gray-600'
          }`}>
            Color Deseado
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            calculateViability();
            setActiveTab('viability');
          }}
          className={`flex-1 p-4 ${activeTab === 'viability' ? 'border-b-2 border-green-500' : ''}`}
        >
          <Text className={`text-center font-medium ${
            activeTab === 'viability' ? 'text-green-600' : 'text-gray-600'
          }`}>
            Viabilidad
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1">
        {activeTab === 'current' && renderCurrentColorAnalysis()}
        {activeTab === 'desired' && renderDesiredColorDefinition()}
        {activeTab === 'viability' && (
          <View className="p-4">
            <Text className="text-xl font-bold text-gray-800 mb-4">
              Análisis de Viabilidad
            </Text>
            {/* Contenido de viabilidad se implementará */}
            <View className="bg-green-50 p-4 rounded-lg border border-green-200">
              <Text className="text-green-800 font-semibold">
                Análisis automático basado en los datos ingresados
              </Text>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Botón de completar */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          onPress={onComplete}
          disabled={!isComplete()}
          className={`p-4 rounded-lg ${
            isComplete() ? 'bg-purple-600' : 'bg-gray-300'
          }`}
        >
          <Text className={`text-center font-semibold ${
            isComplete() ? 'text-white' : 'text-gray-500'
          }`}>
            {isComplete() ? 'Continuar a Formulación' : 'Completa el análisis de color'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ColorAnalysisPhase;
