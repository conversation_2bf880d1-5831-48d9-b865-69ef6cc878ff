import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
} from "react-native";
import {
  Camera,
  Search,
  CheckCircle,
  Clock,
  Zap,
  ArrowRight,
  User,
  Palette,
  Star,
  Sparkles,
  <PERSON>,
  Rainbow,
} from "lucide-react-native";
import ClientSearchBar from "./ClientSearchBar";
import SmartCameraCapture from "./SmartCameraCapture";
import QuickGoalSelector from "./QuickGoalSelector";
import InstantFormulaResult from "./InstantFormulaResult";

const { width, height } = Dimensions.get("window");

// Interfaces para el prototipo
interface Client {
  id: string;
  name: string;
  avatar: string;
  lastService: string;
  lastVisit: string;
  isFrequent: boolean;
}

interface Goal {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  description: string;
}

interface Formula {
  product: string;
  ratio: string;
  time: number;
  confidence: number;
  cost: number;
  instructions: string[];
}

// Datos mock para el prototipo
const mockClients: Client[] = [
  {
    id: "1",
    name: "<PERSON> <PERSON>",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Ana",
    lastService: "Mechas rubias",
    lastVisit: "hace 6 semanas",
    isFrequent: true,
  },
  {
    id: "2",
    name: "María López",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Maria",
    lastService: "Cobertura canas",
    lastVisit: "hace 4 semanas",
    isFrequent: true,
  },
  {
    id: "3",
    name: "Carmen Ruiz",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Carmen",
    lastService: "Cambio de look",
    lastVisit: "hace 2 meses",
    isFrequent: false,
  },
];

const quickGoals: Goal[] = [
  {
    id: "cover_gray",
    label: "Cubrir canas",
    icon: Star,
    color: "bg-yellow-500",
    description: "Cobertura completa de canas",
  },
  {
    id: "subtle_change",
    label: "Cambio sutil",
    icon: Sparkles,
    color: "bg-blue-500",
    description: "Mejora natural del color",
  },
  {
    id: "dramatic",
    label: "Cambio dramático",
    icon: Flame,
    color: "bg-red-500",
    description: "Transformación completa",
  },
  {
    id: "highlights",
    label: "Reflejos",
    icon: Zap,
    color: "bg-purple-500",
    description: "Mechas y reflejos",
  },
  {
    id: "maintenance",
    label: "Mantenimiento",
    icon: CheckCircle,
    color: "bg-green-500",
    description: "Retocar raíces",
  },
  {
    id: "trending",
    label: "Tendencia",
    icon: Rainbow,
    color: "bg-pink-500",
    description: "Color de moda",
  },
];

const ConsultaExpressPrototype = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null);
  const [generatedFormula, setGeneratedFormula] = useState<Formula | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [totalTime, setTotalTime] = useState<number>(0);

  useEffect(() => {
    if (currentStep === 1 && !startTime) {
      setStartTime(new Date());
    }
  }, [currentStep, startTime]);

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    setCurrentStep(2);
  };

  const handlePhotoCapture = () => {
    // Simular captura de foto
    setCapturedPhoto("captured_photo_url");
    setCurrentStep(3);
  };

  const handleGoalSelect = (goal: Goal) => {
    setSelectedGoal(goal);
    generateFormula(goal);
  };

  const generateFormula = async (goal: Goal) => {
    setIsAnalyzing(true);
    
    // Simular análisis IA (2-3 segundos)
    await new Promise(resolve => setTimeout(resolve, 2500));
    
    // Generar fórmula mock basada en el objetivo
    const mockFormula: Formula = {
      product: goal.id === "cover_gray" ? "Wella Koleston 6/0 + 20vol" : 
               goal.id === "dramatic" ? "Wella Blondor + 30vol" :
               "Wella Color Touch 8/3 + 13vol",
      ratio: "1:1.5",
      time: goal.id === "dramatic" ? 45 : 35,
      confidence: Math.floor(Math.random() * 15) + 85, // 85-99%
      cost: goal.id === "dramatic" ? 65 : 45,
      instructions: [
        "Aplicar primero en raíces",
        "Extender a medios y puntas",
        "Controlar cada 10 minutos",
        "Aclarar con agua tibia"
      ]
    };

    setGeneratedFormula(mockFormula);
    setIsAnalyzing(false);
    setCurrentStep(4);
    
    // Calcular tiempo total
    if (startTime) {
      const endTime = new Date();
      const timeDiff = Math.round((endTime.getTime() - startTime.getTime()) / 1000);
      setTotalTime(timeDiff);
    }
  };

  const resetConsultation = () => {
    setCurrentStep(1);
    setSelectedClient(null);
    setCapturedPhoto(null);
    setSelectedGoal(null);
    setGeneratedFormula(null);
    setIsAnalyzing(false);
    setStartTime(null);
    setTotalTime(0);
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return "Seleccionar Cliente";
      case 2: return "Capturar Imagen";
      case 3: return "Objetivo del Servicio";
      case 4: return "Fórmula Lista";
      default: return "Consulta Express";
    }
  };

  const getButtonText = () => {
    switch (currentStep) {
      case 2: return "📸 Capturar Foto del Cabello";
      case 3: return "Selecciona el objetivo del servicio";
      case 4: return "✅ Aplicar Fórmula";
      default: return "Continuar";
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* HEADER: Progreso y cliente seleccionado */}
      <View className="bg-white px-4 py-3 shadow-sm border-b border-gray-100">
        <View className="flex-row items-center justify-between mb-2">
          <Text className="text-lg font-bold text-gray-800">{getStepTitle()}</Text>
          <View className="flex-row items-center">
            <Clock size={16} color="#6B7280" />
            <Text className="text-sm text-gray-600 ml-1">
              {totalTime > 0 ? `${totalTime}s` : "En progreso..."}
            </Text>
          </View>
        </View>
        
        {/* Barra de progreso */}
        <View className="w-full h-2 bg-gray-200 rounded-full mb-3">
          <View 
            className="h-full bg-blue-500 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / 4) * 100}%` }}
          />
        </View>

        {/* Cliente seleccionado */}
        {selectedClient && (
          <View className="flex-row items-center bg-blue-50 p-3 rounded-lg">
            <View className="w-10 h-10 bg-blue-200 rounded-full items-center justify-center mr-3">
              <User size={20} color="#3B82F6" />
            </View>
            <View className="flex-1">
              <Text className="font-semibold text-gray-800">{selectedClient.name}</Text>
              <Text className="text-sm text-gray-600">
                Última: {selectedClient.lastService} • {selectedClient.lastVisit}
              </Text>
            </View>
            {selectedClient.isFrequent && (
              <View className="bg-yellow-100 px-2 py-1 rounded-full">
                <Text className="text-xs text-yellow-800 font-medium">Frecuente</Text>
              </View>
            )}
          </View>
        )}
      </View>

      {/* MAIN CONTENT */}
      <ScrollView className="flex-1 p-4">
        {/* PASO 1: Selección de Cliente */}
        {currentStep === 1 && (
          <View>
            <ClientSearchBar
              onSelect={handleClientSelect}
              onCreateNew={(name) => {
                const newClient: Client = {
                  id: `new_${Date.now()}`,
                  name,
                  avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`,
                  lastService: "Nuevo cliente",
                  lastVisit: "Primera visita",
                  isFrequent: false,
                };
                handleClientSelect(newClient);
              }}
              placeholder="Buscar cliente por nombre, teléfono..."
              showFrequentFirst={true}
              maxResults={8}
            />
          </View>
        )}

        {/* PASO 2: Captura de Imagen */}
        {currentStep === 2 && (
          <SmartCameraCapture
            onCapture={(imageUri) => {
              setCapturedPhoto(imageUri);
              setCurrentStep(3);
            }}
            onAnalysisComplete={(analysis) => {
              console.log("Análisis completado:", analysis);
            }}
            showRealTimeFeedback={true}
            autoAnalyze={true}
          />
        )}

        {/* PASO 3: Selección de Objetivo */}
        {currentStep === 3 && (
          <QuickGoalSelector
            onSelect={handleGoalSelect}
            selectedGoal={selectedGoal}
            showDetails={true}
            maxColumns={2}
          />
        )}

        {/* PASO 4: Resultado - Fórmula */}
        {currentStep === 4 && (
          <View>
            {isAnalyzing ? (
              <View className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 items-center">
                <View className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4" />
                <Text className="text-lg font-semibold text-gray-800 mb-2">
                  Analizando con IA...
                </Text>
                <Text className="text-gray-600 text-center">
                  Procesando imagen y generando fórmula personalizada
                </Text>
              </View>
            ) : generatedFormula && (
              <InstantFormulaResult
                formula={{
                  id: "formula_001",
                  product: generatedFormula.product,
                  brand: "Wella",
                  ratio: generatedFormula.ratio,
                  volume: "20vol",
                  time: generatedFormula.time,
                  confidence: generatedFormula.confidence,
                  cost: generatedFormula.cost,
                  difficulty: generatedFormula.difficulty,
                  instructions: generatedFormula.instructions,
                  warnings: generatedFormula.warnings,
                }}
                clientName={selectedClient?.name}
                goalDescription={selectedGoal?.description}
                onApply={() => {
                  Alert.alert(
                    "Fórmula Aplicada",
                    "La fórmula ha sido guardada en el historial del cliente.",
                    [{ text: "Nueva Consulta", onPress: resetConsultation }]
                  );
                }}
                onEdit={() => {
                  Alert.alert("Editar", "Función de edición en desarrollo");
                }}
                onSave={() => {
                  Alert.alert("Guardado", "Fórmula guardada en el historial");
                }}
                analysisTime={totalTime}
                showStrandTest={true}
              />
            )}
          </View>
        )}
      </ScrollView>

      {/* FOOTER: Botón de acción principal */}
      {currentStep < 4 && !isAnalyzing && (
        <View className="bg-white p-4 border-t border-gray-100">
          <TouchableOpacity
            className={`py-4 px-6 rounded-lg flex-row items-center justify-center ${
              currentStep === 2 ? "bg-blue-500" : "bg-gray-300"
            }`}
            onPress={currentStep === 2 ? handlePhotoCapture : undefined}
            disabled={currentStep !== 2}
          >
            <Text className={`font-semibold ${currentStep === 2 ? "text-white" : "text-gray-500"}`}>
              {getButtonText()}
            </Text>
            {currentStep === 2 && <ArrowRight size={20} color="white" className="ml-2" />}
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default ConsultaExpressPrototype;
