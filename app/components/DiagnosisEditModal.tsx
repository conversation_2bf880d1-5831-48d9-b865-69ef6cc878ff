import React, { useState } from "react";
import { View, Text, Modal, ScrollView, TouchableOpacity, TextInput } from "react-native";
import { X, Save, AlertTriangle } from "lucide-react-native";

interface DiagnosisEditModalProps {
  visible: boolean;
  diagnosisData: any;
  onSave: (updatedData: any) => void;
  onClose: () => void;
}

const DiagnosisEditModal = ({
  visible,
  diagnosisData,
  onSave,
  onClose,
}: DiagnosisEditModalProps) => {
  const [editedData, setEditedData] = useState(diagnosisData || {});

  const updateField = (path: string, value: any) => {
    const keys = path.split('.');
    const newData = { ...editedData };
    let current = newData;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) current[keys[i]] = {};
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    setEditedData(newData);
  };

  const getFieldValue = (path: string) => {
    const keys = path.split('.');
    let current = editedData;
    
    for (const key of keys) {
      if (!current || current[key] === undefined) return '';
      current = current[key];
    }
    
    return current;
  };

  const renderSlider = (label: string, path: string, min: number, max: number) => (
    <View className="mb-4">
      <Text className="font-semibold mb-2">{label}</Text>
      <View className="flex-row items-center">
        <Text className="text-gray-500 mr-2">{min}</Text>
        <View className="flex-1 h-6 bg-gray-200 rounded-full mx-2">
          <TouchableOpacity
            className="h-6 bg-purple-500 rounded-full flex items-center justify-center"
            style={{ width: `${((getFieldValue(path) - min) / (max - min)) * 100}%` }}
          >
            <View className="w-4 h-4 bg-white rounded-full" />
          </TouchableOpacity>
        </View>
        <Text className="text-gray-500 ml-2">{max}</Text>
        <Text className="font-bold ml-3 w-8">{getFieldValue(path)}</Text>
      </View>
    </View>
  );

  const renderSelect = (label: string, path: string, options: string[]) => (
    <View className="mb-4">
      <Text className="font-semibold mb-2">{label}</Text>
      <View className="flex-row flex-wrap">
        {options.map((option) => (
          <TouchableOpacity
            key={option}
            onPress={() => updateField(path, option)}
            className={`px-3 py-2 rounded-full mr-2 mb-2 ${
              getFieldValue(path) === option
                ? "bg-purple-500"
                : "bg-gray-200"
            }`}
          >
            <Text
              className={`text-sm ${
                getFieldValue(path) === option
                  ? "text-white font-semibold"
                  : "text-gray-700"
              }`}
            >
              {option}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderTextInput = (label: string, path: string, placeholder?: string, multiline?: boolean) => (
    <View className="mb-4">
      <Text className="font-semibold mb-2">{label}</Text>
      <TextInput
        className={`border border-gray-300 rounded-lg p-3 bg-white ${multiline ? 'min-h-[80px]' : ''}`}
        value={getFieldValue(path)?.toString() || ''}
        onChangeText={(text) => updateField(path, text)}
        placeholder={placeholder}
        multiline={multiline}
        textAlignVertical={multiline ? "top" : "center"}
      />
    </View>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View className="flex-1 bg-gray-50">
        {/* Header */}
        <View className="bg-white border-b border-gray-200 px-4 py-3">
          <View className="flex-row items-center justify-between">
            <TouchableOpacity onPress={onClose}>
              <X size={24} color="#374151" />
            </TouchableOpacity>
            <Text className="text-xl font-bold text-gray-800">Editar Diagnóstico</Text>
            <TouchableOpacity
              onPress={() => {
                onSave(editedData);
                onClose();
              }}
              className="bg-purple-500 px-4 py-2 rounded-lg"
            >
              <Text className="text-white font-semibold">Guardar</Text>
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView className="flex-1 p-4">
          {/* Información General */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-bold mb-4 text-purple-800">Información General</Text>
            
            {renderSlider("Nivel Natural", "naturalLevel", 1, 10)}
            {renderSelect("Subtono", "undertone", ["warm", "cool", "neutral"])}
            {renderSelect("Porosidad General", "porosity", ["low", "medium", "high"])}
            {renderSelect("Condición General", "condition", ["healthy", "normal", "damaged", "severely_damaged"])}
          </View>

          {/* Análisis por Zonas */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-bold mb-4 text-purple-800">Análisis por Zonas</Text>
            
            <Text className="font-semibold text-gray-700 mb-3">Raíces</Text>
            {renderSlider("Nivel Natural Raíces", "zoneAnalysis.roots.naturalLevel", 1, 10)}
            {renderSelect("Condición Raíces", "zoneAnalysis.roots.condition", ["virgin", "regrowth", "previously_treated"])}
            {renderSlider("% Canas en Raíces", "zoneAnalysis.roots.grayPercentage", 0, 100)}
            {renderTextInput("Notas Raíces", "zoneAnalysis.roots.notes", "Observaciones específicas de las raíces")}

            <Text className="font-semibold text-gray-700 mb-3 mt-6">Medios</Text>
            {renderSlider("Nivel Actual Medios", "zoneAnalysis.midLengths.level", 1, 12)}
            {renderSelect("Condición Medios", "zoneAnalysis.midLengths.condition", ["healthy", "normal", "damaged", "severely_damaged"])}
            {renderSelect("Porosidad Medios", "zoneAnalysis.midLengths.porosity", ["low", "medium", "high"])}
            {renderTextInput("Notas Medios", "zoneAnalysis.midLengths.notes", "Observaciones de los medios")}

            <Text className="font-semibold text-gray-700 mb-3 mt-6">Puntas</Text>
            {renderSlider("Nivel Actual Puntas", "zoneAnalysis.ends.level", 1, 12)}
            {renderSelect("Condición Puntas", "zoneAnalysis.ends.condition", ["healthy", "normal", "damaged", "severely_damaged"])}
            {renderSelect("Porosidad Puntas", "zoneAnalysis.ends.porosity", ["low", "medium", "high"])}
            {renderSelect("Elasticidad Puntas", "zoneAnalysis.ends.elasticity", ["excellent", "good", "poor", "very_poor"])}
            {renderTextInput("Notas Puntas", "zoneAnalysis.ends.notes", "Observaciones de las puntas")}
          </View>

          {/* Análisis de Canas */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-bold mb-4 text-purple-800">Análisis de Canas</Text>
            
            {renderSlider("Porcentaje Total", "grayAnalysis.totalPercentage", 0, 100)}
            {renderSelect("Distribución", "grayAnalysis.distribution", ["temples", "crown", "scattered", "uniform"])}
            {renderSelect("Resistencia", "grayAnalysis.resistance", ["low", "medium", "high", "very_high"])}
            {renderSelect("Textura", "grayAnalysis.texture", ["coarse", "normal", "fine"])}
            {renderTextInput("Notas Canas", "grayAnalysis.notes", "Observaciones sobre las canas", true)}
          </View>

          {/* Estructura del Cabello */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-bold mb-4 text-purple-800">Estructura del Cabello</Text>
            
            {renderSelect("Grosor", "hairStructure.thickness", ["fine", "medium", "coarse"])}
            {renderSelect("Densidad", "hairStructure.density", ["low", "medium", "high"])}
            {renderSelect("Elasticidad", "hairStructure.elasticity", ["excellent", "good", "poor", "very_poor"])}
            {renderSelect("Patrón de Rizo", "hairStructure.curl_pattern", ["straight", "wavy", "curly", "coily"])}
            {renderTextInput("Notas Estructura", "hairStructure.notes", "Observaciones sobre la estructura", true)}
          </View>

          {/* Recomendaciones Profesionales */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-bold mb-4 text-purple-800">Recomendaciones Profesionales</Text>
            
            <View className="mb-4">
              <Text className="font-semibold mb-2">¿Requiere Pretratamiento?</Text>
              <View className="flex-row">
                <TouchableOpacity
                  onPress={() => updateField("professionalRecommendations.pretreatmentNeeded", true)}
                  className={`px-4 py-2 rounded-lg mr-2 ${
                    getFieldValue("professionalRecommendations.pretreatmentNeeded")
                      ? "bg-red-500"
                      : "bg-gray-200"
                  }`}
                >
                  <Text className={getFieldValue("professionalRecommendations.pretreatmentNeeded") ? "text-white" : "text-gray-700"}>
                    Sí
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => updateField("professionalRecommendations.pretreatmentNeeded", false)}
                  className={`px-4 py-2 rounded-lg ${
                    !getFieldValue("professionalRecommendations.pretreatmentNeeded")
                      ? "bg-green-500"
                      : "bg-gray-200"
                  }`}
                >
                  <Text className={!getFieldValue("professionalRecommendations.pretreatmentNeeded") ? "text-white" : "text-gray-700"}>
                    No
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {getFieldValue("professionalRecommendations.pretreatmentNeeded") && (
              renderTextInput("Tipo de Pretratamiento", "professionalRecommendations.pretreatmentType", "Especifica el pretratamiento necesario")
            )}

            {renderSelect("Oxidante Recomendado", "professionalRecommendations.developerStrength", ["10vol", "20vol", "30vol", "40vol"])}
            
            <View className="mb-4">
              <Text className="font-semibold mb-2">Ajuste de Tiempo (minutos)</Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3 bg-white"
                value={getFieldValue("professionalRecommendations.processingTimeAdjustment")?.toString() || '0'}
                onChangeText={(text) => updateField("professionalRecommendations.processingTimeAdjustment", parseInt(text) || 0)}
                keyboardType="numeric"
                placeholder="0"
              />
              <Text className="text-gray-500 text-sm mt-1">Positivo para más tiempo, negativo para menos</Text>
            </View>
          </View>

          <View className="h-20" />
        </ScrollView>
      </View>
    </Modal>
  );
};

export default DiagnosisEditModal;
