import React from "react";
import { View, Text } from "react-native";
import { CheckCircle, AlertTriangle, Clock, Eye, TestTube } from "lucide-react-native";

interface DiagnosisStatusIndicatorProps {
  diagnosisData: any;
}

const DiagnosisStatusIndicator = ({ diagnosisData }: DiagnosisStatusIndicatorProps) => {
  if (!diagnosisData) return null;

  const verification = diagnosisData.verification || {};
  const physicalTests = diagnosisData.physicalTests || {};
  
  const statusItems = [
    {
      label: "Análisis IA",
      completed: verification.aiAnalysisComplete,
      icon: Eye,
      color: verification.aiAnalysisComplete ? "green" : "gray"
    },
    {
      label: "Tests Físicos",
      completed: physicalTests.porosityTest?.performed && physicalTests.elasticityTest?.performed,
      icon: TestTube,
      color: (physicalTests.porosityTest?.performed && physicalTests.elasticityTest?.performed) ? "green" : "orange"
    },
    {
      label: "Revisión Estilista",
      completed: verification.stylistReviewComplete,
      icon: CheckCircle,
      color: verification.stylistReviewComplete ? "green" : "red"
    },
    {
      label: "Info Cliente",
      completed: verification.clientInfoComplete,
      icon: AlertTriangle,
      color: verification.clientInfoComplete ? "green" : "orange"
    }
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case "green":
        return "bg-green-100 text-green-800 border-green-200";
      case "orange":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "red":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getIconColor = (color: string) => {
    switch (color) {
      case "green":
        return "#16a34a";
      case "orange":
        return "#ea580c";
      case "red":
        return "#dc2626";
      default:
        return "#6b7280";
    }
  };

  const completedCount = statusItems.filter(item => item.completed).length;
  const totalCount = statusItems.length;
  const completionPercentage = (completedCount / totalCount) * 100;

  return (
    <View className="bg-white rounded-lg p-4 mb-4 border border-gray-200">
      <View className="flex-row items-center justify-between mb-3">
        <Text className="font-bold text-gray-800">Estado del Diagnóstico</Text>
        <Text className="text-sm font-semibold text-purple-600">
          {completedCount}/{totalCount}
        </Text>
      </View>

      {/* Progress Bar */}
      <View className="w-full h-2 bg-gray-200 rounded-full mb-4">
        <View 
          className="h-2 bg-purple-500 rounded-full"
          style={{ width: `${completionPercentage}%` }}
        />
      </View>

      {/* Status Items */}
      <View className="flex-row flex-wrap">
        {statusItems.map((item, index) => {
          const IconComponent = item.icon;
          return (
            <View
              key={index}
              className={`flex-row items-center px-3 py-2 rounded-full mr-2 mb-2 border ${getColorClasses(item.color)}`}
            >
              <IconComponent size={14} color={getIconColor(item.color)} />
              <Text className={`ml-1 text-xs font-medium ${getColorClasses(item.color).split(' ')[1]}`}>
                {item.label}
              </Text>
              {item.completed && (
                <View className="ml-1 w-3 h-3 bg-green-500 rounded-full flex items-center justify-center">
                  <Text className="text-white text-xs">✓</Text>
                </View>
              )}
            </View>
          );
        })}
      </View>

      {/* Warning if not ready */}
      {!verification.readyForFormulation && (
        <View className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <View className="flex-row items-center">
            <AlertTriangle size={16} color="#d97706" />
            <Text className="ml-2 text-yellow-800 font-medium text-sm">
              Verificación Pendiente
            </Text>
          </View>
          <Text className="text-yellow-700 text-xs mt-1">
            Se recomienda completar la revisión profesional antes de generar la formulación.
          </Text>
        </View>
      )}

      {/* Ready indicator */}
      {verification.readyForFormulation && (
        <View className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
          <View className="flex-row items-center">
            <CheckCircle size={16} color="#16a34a" />
            <Text className="ml-2 text-green-800 font-medium text-sm">
              Listo para Formulación
            </Text>
          </View>
          <Text className="text-green-700 text-xs mt-1">
            Diagnóstico completo y verificado por estilista profesional.
          </Text>
        </View>
      )}

      {/* Last modified info */}
      {verification.lastModified && (
        <View className="mt-2 pt-2 border-t border-gray-100">
          <Text className="text-xs text-gray-500">
            Última modificación: {verification.lastModifiedBy === "ai" ? "IA" : "Estilista"} • {" "}
            {new Date(verification.lastModified).toLocaleString()}
          </Text>
        </View>
      )}
    </View>
  );
};

export default DiagnosisStatusIndicator;
