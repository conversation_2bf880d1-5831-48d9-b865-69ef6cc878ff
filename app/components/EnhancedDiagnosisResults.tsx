import React, { useState } from "react";
import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import { ChevronDown, ChevronRight, AlertTriangle, CheckCircle, Info } from "lucide-react-native";
import DiagnosisStatusIndicator from "./DiagnosisStatusIndicator";

interface EnhancedDiagnosisResultsProps {
  diagnosisData: any; // Will use the enhanced DiagnosisData interface
  onConfirm?: () => void;
  onEdit?: () => void;
}

const EnhancedDiagnosisResults = ({
  diagnosisData,
  onConfirm = () => {},
  onEdit = () => {},
}: EnhancedDiagnosisResultsProps) => {
  const [expandedSections, setExpandedSections] = useState({
    zoneAnalysis: true,
    grayAnalysis: false,
    hairStructure: false,
    chemicalHistory: false,
    recommendations: true,
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case "excellent":
      case "healthy":
      case "virgin":
        return "text-green-600 bg-green-50";
      case "good":
      case "normal":
      case "regrowth":
        return "text-blue-600 bg-blue-50";
      case "poor":
      case "damaged":
      case "previously_treated":
        return "text-orange-600 bg-orange-50";
      case "very_poor":
      case "severely_damaged":
        return "text-red-600 bg-red-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return "bg-green-500";
    if (confidence >= 80) return "bg-yellow-500";
    if (confidence >= 70) return "bg-orange-500";
    return "bg-red-500";
  };

  const renderSectionHeader = (title: string, sectionKey: string, icon?: React.ReactNode) => (
    <TouchableOpacity
      onPress={() => toggleSection(sectionKey)}
      className="flex-row items-center justify-between p-3 bg-gray-100 rounded-lg mb-2"
    >
      <View className="flex-row items-center">
        {icon}
        <Text className="text-lg font-semibold ml-2">{title}</Text>
      </View>
      {expandedSections[sectionKey] ? (
        <ChevronDown size={20} color="#666" />
      ) : (
        <ChevronRight size={20} color="#666" />
      )}
    </TouchableOpacity>
  );

  const renderZoneCard = (title: string, data: any, zoneType: "roots" | "midLengths" | "ends") => (
    <View className="bg-white border border-gray-200 rounded-lg p-4 mb-3">
      <Text className="font-bold text-lg mb-3 text-purple-800">{title}</Text>
      
      {zoneType === "roots" && (
        <>
          <View className="flex-row justify-between mb-2">
            <Text className="text-gray-600">Nivel Natural:</Text>
            <Text className="font-semibold">{data.naturalLevel}/10</Text>
          </View>
          <View className="flex-row justify-between mb-2">
            <Text className="text-gray-600">Condición:</Text>
            <Text className={`px-2 py-1 rounded-full text-xs font-medium ${getConditionColor(data.condition)}`}>
              {data.condition}
            </Text>
          </View>
          <View className="flex-row justify-between mb-2">
            <Text className="text-gray-600">% Canas:</Text>
            <Text className="font-semibold">{data.grayPercentage}%</Text>
          </View>
        </>
      )}
      
      {(zoneType === "midLengths" || zoneType === "ends") && (
        <>
          <View className="flex-row justify-between mb-2">
            <Text className="text-gray-600">Nivel Actual:</Text>
            <Text className="font-semibold">{data.level}/10</Text>
          </View>
          <View className="flex-row justify-between mb-2">
            <Text className="text-gray-600">Condición:</Text>
            <Text className={`px-2 py-1 rounded-full text-xs font-medium ${getConditionColor(data.condition)}`}>
              {data.condition}
            </Text>
          </View>
          <View className="flex-row justify-between mb-2">
            <Text className="text-gray-600">Porosidad:</Text>
            <Text className="font-semibold capitalize">{data.porosity}</Text>
          </View>
        </>
      )}
      
      {zoneType === "ends" && (
        <View className="flex-row justify-between mb-2">
          <Text className="text-gray-600">Elasticidad:</Text>
          <Text className={`px-2 py-1 rounded-full text-xs font-medium ${getConditionColor(data.elasticity)}`}>
            {data.elasticity}
          </Text>
        </View>
      )}
      
      {data.notes && (
        <View className="mt-2 p-2 bg-blue-50 rounded">
          <Text className="text-blue-800 text-sm">{data.notes}</Text>
        </View>
      )}
    </View>
  );

  return (
    <ScrollView className="flex-1 bg-gray-50">
      <View className="p-4">
        {/* Status Indicator */}
        <DiagnosisStatusIndicator diagnosisData={diagnosisData} />

        {/* Header con confianza del análisis */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="text-2xl font-bold text-center mb-2 text-purple-800">
            Diagnóstico Profesional IA
          </Text>
          <View className="items-center mb-4">
            <Text className="text-sm text-gray-600">
              Confianza del Análisis: {diagnosisData.analysisConfidence}%
            </Text>
            <View className="w-32 h-2 bg-gray-200 rounded-full mt-1">
              <View
                className={`h-full rounded-full ${getConfidenceColor(diagnosisData.analysisConfidence)}`}
                style={{ width: `${diagnosisData.analysisConfidence}%` }}
              />
            </View>
          </View>
          
          {/* Información básica en cards pequeñas */}
          <View className="flex-row flex-wrap justify-between">
            <View className="bg-purple-50 p-3 rounded-lg mb-2 w-[48%]">
              <Text className="text-xs text-purple-600 font-medium">NIVEL NATURAL</Text>
              <Text className="text-lg font-bold text-purple-800">{diagnosisData.naturalLevel}/10</Text>
            </View>
            <View className="bg-blue-50 p-3 rounded-lg mb-2 w-[48%]">
              <Text className="text-xs text-blue-600 font-medium">SUBTONO</Text>
              <Text className="text-lg font-bold text-blue-800 capitalize">{diagnosisData.undertone}</Text>
            </View>
          </View>
        </View>

        {/* Análisis por Zonas */}
        {renderSectionHeader("Análisis por Zonas", "zoneAnalysis", <Info size={20} color="#8B5CF6" />)}
        {expandedSections.zoneAnalysis && (
          <View className="mb-4">
            {renderZoneCard("Raíces", diagnosisData.zoneAnalysis?.roots, "roots")}
            {renderZoneCard("Medios", diagnosisData.zoneAnalysis?.midLengths, "midLengths")}
            {renderZoneCard("Puntas", diagnosisData.zoneAnalysis?.ends, "ends")}
          </View>
        )}

        {/* Análisis de Canas */}
        {renderSectionHeader("Análisis de Canas", "grayAnalysis", <AlertTriangle size={20} color="#F59E0B" />)}
        {expandedSections.grayAnalysis && diagnosisData.grayAnalysis && (
          <View className="bg-white rounded-lg p-4 mb-4">
            <View className="flex-row justify-between mb-3">
              <Text className="text-gray-600">Porcentaje Total:</Text>
              <Text className="font-bold text-lg">{diagnosisData.grayAnalysis.totalPercentage}%</Text>
            </View>
            <View className="flex-row justify-between mb-3">
              <Text className="text-gray-600">Distribución:</Text>
              <Text className="font-semibold capitalize">{diagnosisData.grayAnalysis.distribution}</Text>
            </View>
            <View className="flex-row justify-between mb-3">
              <Text className="text-gray-600">Resistencia:</Text>
              <Text className={`px-2 py-1 rounded-full text-xs font-medium ${getConditionColor(diagnosisData.grayAnalysis.resistance)}`}>
                {diagnosisData.grayAnalysis.resistance}
              </Text>
            </View>
            <View className="flex-row justify-between mb-3">
              <Text className="text-gray-600">Textura:</Text>
              <Text className="font-semibold capitalize">{diagnosisData.grayAnalysis.texture}</Text>
            </View>
          </View>
        )}

        {/* Estructura del Cabello */}
        {renderSectionHeader("Estructura del Cabello", "hairStructure", <CheckCircle size={20} color="#10B981" />)}
        {expandedSections.hairStructure && diagnosisData.hairStructure && (
          <View className="bg-white rounded-lg p-4 mb-4">
            <View className="grid grid-cols-2 gap-3">
              <View className="bg-gray-50 p-3 rounded">
                <Text className="text-xs text-gray-500 font-medium">GROSOR</Text>
                <Text className="font-semibold capitalize">{diagnosisData.hairStructure.thickness}</Text>
              </View>
              <View className="bg-gray-50 p-3 rounded">
                <Text className="text-xs text-gray-500 font-medium">DENSIDAD</Text>
                <Text className="font-semibold capitalize">{diagnosisData.hairStructure.density}</Text>
              </View>
              <View className="bg-gray-50 p-3 rounded">
                <Text className="text-xs text-gray-500 font-medium">ELASTICIDAD</Text>
                <Text className="font-semibold capitalize">{diagnosisData.hairStructure.elasticity}</Text>
              </View>
              <View className="bg-gray-50 p-3 rounded">
                <Text className="text-xs text-gray-500 font-medium">PATRÓN</Text>
                <Text className="font-semibold capitalize">{diagnosisData.hairStructure.curl_pattern}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Recomendaciones Profesionales */}
        {renderSectionHeader("Recomendaciones Profesionales", "recommendations", <AlertTriangle size={20} color="#EF4444" />)}
        {expandedSections.recommendations && diagnosisData.professionalRecommendations && (
          <View className="bg-white rounded-lg p-4 mb-4">
            {diagnosisData.professionalRecommendations.pretreatmentNeeded && (
              <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-3">
                <Text className="font-bold text-yellow-800 mb-1">⚠️ Pretratamiento Requerido</Text>
                <Text className="text-yellow-700">{diagnosisData.professionalRecommendations.pretreatmentType}</Text>
              </View>
            )}
            
            <View className="flex-row justify-between mb-3">
              <Text className="text-gray-600">Oxidante Recomendado:</Text>
              <Text className="font-bold text-purple-800">{diagnosisData.professionalRecommendations.developerStrength}</Text>
            </View>
            
            <View className="flex-row justify-between mb-3">
              <Text className="text-gray-600">Ajuste de Tiempo:</Text>
              <Text className="font-semibold">
                {diagnosisData.professionalRecommendations.processingTimeAdjustment > 0 ? '+' : ''}
                {diagnosisData.professionalRecommendations.processingTimeAdjustment} min
              </Text>
            </View>
            
            {diagnosisData.professionalRecommendations.specialConsiderations?.length > 0 && (
              <View className="mt-3">
                <Text className="font-semibold mb-2">Consideraciones Especiales:</Text>
                {diagnosisData.professionalRecommendations.specialConsiderations.map((consideration: string, index: number) => (
                  <Text key={index} className="text-gray-700 text-sm py-1">• {consideration}</Text>
                ))}
              </View>
            )}
          </View>
        )}

        {/* Botones de acción */}
        <View className="flex-row space-x-3 mt-6">
          <TouchableOpacity
            onPress={onEdit}
            className="flex-1 bg-gray-200 py-3 rounded-lg"
          >
            <Text className="text-center font-semibold text-gray-700">Editar Diagnóstico</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onConfirm}
            className="flex-1 bg-purple-600 py-3 rounded-lg"
          >
            <Text className="text-center font-semibold text-white">Confirmar y Continuar</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

export default EnhancedDiagnosisResults;
