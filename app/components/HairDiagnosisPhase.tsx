import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
} from "react-native";
import {
  Camera,
  Plus,
  Info,
  CheckCircle,
  AlertTriangle,
  Eye,
  Droplets,
  Clock,
  Scissors,
} from "lucide-react-native";

interface DiagnosisData {
  naturalLevel: number | null;
  undertone: string | null;
  porosity: string | null;
  chemicalHistory: string[];
  hairCondition: string | null;
  elasticity: string | null;
  images: string[];
  pH: string | null;
  texture: string | null;
  density: string | null;
}

interface HairDiagnosisPhaseProps {
  data: DiagnosisData;
  onUpdate: (data: DiagnosisData) => void;
  onComplete: () => void;
}

const HairDiagnosisPhase: React.FC<HairDiagnosisPhaseProps> = ({
  data,
  onUpdate,
  onComplete,
}) => {
  const [activeSection, setActiveSection] = useState<string>('images');

  // Datos de referencia profesional
  const naturalLevels = [
    { level: 1, name: "Negro", description: "Negro azulado" },
    { level: 2, name: "Castaño <PERSON>", description: "Castaño casi negro" },
    { level: 3, name: "Castaño <PERSON>ro", description: "Castaño chocolate" },
    { level: 4, name: "Castaño Medio", description: "Castaño natural" },
    { level: 5, name: "Castaño Claro", description: "Castaño dorado" },
    { level: 6, name: "Rubio Oscuro", description: "Rubio ceniza oscuro" },
    { level: 7, name: "Rubio Medio", description: "Rubio natural" },
    { level: 8, name: "Rubio Claro", description: "Rubio dorado claro" },
    { level: 9, name: "Rubio Muy Claro", description: "Rubio platino" },
    { level: 10, name: "Rubio Extra Claro", description: "Rubio platino claro" },
  ];

  const undertones = [
    { id: 'neutral', name: 'Neutro', description: 'Sin reflejos dominantes' },
    { id: 'ash', name: 'Ceniza', description: 'Reflejos fríos, verdosos' },
    { id: 'golden', name: 'Dorado', description: 'Reflejos cálidos, amarillos' },
    { id: 'copper', name: 'Cobrizo', description: 'Reflejos naranjas' },
    { id: 'red', name: 'Rojizo', description: 'Reflejos rojos' },
    { id: 'violet', name: 'Violeta', description: 'Reflejos violáceos' },
  ];

  const porosityLevels = [
    { id: 'low', name: 'Baja', description: 'Cutícula cerrada, resistente al color' },
    { id: 'medium', name: 'Media', description: 'Cutícula normal, absorción equilibrada' },
    { id: 'high', name: 'Alta', description: 'Cutícula abierta, absorbe rápido' },
  ];

  const chemicalTreatments = [
    'Decoloración',
    'Tinte permanente',
    'Tinte semipermanente',
    'Mechas/Highlights',
    'Balayage',
    'Alisado químico',
    'Permanente',
    'Botox capilar',
    'Keratina',
    'Decapado',
  ];

  const handleImageCapture = () => {
    Alert.alert(
      "Captura de Imagen",
      "Selecciona el tipo de imagen a capturar:",
      [
        { text: "Frontal", onPress: () => captureImage('frontal') },
        { text: "Coronilla", onPress: () => captureImage('coronilla') },
        { text: "Lateral", onPress: () => captureImage('lateral') },
        { text: "Nuca", onPress: () => captureImage('nuca') },
        { text: "Cancelar", style: "cancel" },
      ]
    );
  };

  const captureImage = (type: string) => {
    // Simulación de captura de imagen
    const newImage = `image_${type}_${Date.now()}`;
    onUpdate({
      ...data,
      images: [...data.images, newImage],
    });
  };

  const updateField = (field: keyof DiagnosisData, value: any) => {
    onUpdate({
      ...data,
      [field]: value,
    });
  };

  const toggleChemicalTreatment = (treatment: string) => {
    const currentHistory = data.chemicalHistory || [];
    const newHistory = currentHistory.includes(treatment)
      ? currentHistory.filter(t => t !== treatment)
      : [...currentHistory, treatment];
    
    updateField('chemicalHistory', newHistory);
  };

  const isComplete = () => {
    return (
      data.naturalLevel !== null &&
      data.undertone !== null &&
      data.porosity !== null &&
      data.hairCondition !== null &&
      data.images.length >= 3
    );
  };

  const renderImageCapture = () => (
    <View className="mb-6">
      <View className="flex-row items-center mb-3">
        <Camera size={20} color="#3B82F6" />
        <Text className="text-lg font-semibold text-gray-800 ml-2">
          Captura Multi-Imagen
        </Text>
        <View className="bg-blue-100 px-2 py-1 rounded-full ml-2">
          <Text className="text-blue-800 text-xs font-medium">
            {data.images.length}/7
          </Text>
        </View>
      </View>

      <View className="bg-blue-50 p-3 rounded-lg border border-blue-200 mb-4">
        <Text className="text-blue-800 font-medium mb-1">Guía de Captura:</Text>
        <Text className="text-blue-700 text-sm">
          • Mínimo 3 imágenes (frontal, coronilla, nuca){'\n'}
          • Buena iluminación natural{'\n'}
          • Sin filtros ni efectos{'\n'}
          • Cabello seco y peinado natural
        </Text>
      </View>

      <View className="flex-row flex-wrap justify-between mb-4">
        {data.images.map((image, index) => (
          <View key={index} className="w-[23%] aspect-square bg-gray-200 rounded-lg mb-2 items-center justify-center">
            <Camera size={20} color="#6B7280" />
            <Text className="text-xs text-gray-600 mt-1">Img {index + 1}</Text>
          </View>
        ))}
        
        {data.images.length < 7 && (
          <TouchableOpacity
            onPress={handleImageCapture}
            className="w-[23%] aspect-square bg-blue-100 rounded-lg border-2 border-dashed border-blue-300 items-center justify-center"
          >
            <Plus size={24} color="#3B82F6" />
            <Text className="text-blue-600 text-xs mt-1">Añadir</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderNaturalLevel = () => (
    <View className="mb-6">
      <View className="flex-row items-center mb-3">
        <Eye size={20} color="#8B5CF6" />
        <Text className="text-lg font-semibold text-gray-800 ml-2">
          Nivel Base Natural
        </Text>
      </View>

      <View className="flex-row flex-wrap justify-between">
        {naturalLevels.map((level) => (
          <TouchableOpacity
            key={level.level}
            onPress={() => updateField('naturalLevel', level.level)}
            className={`w-[18%] aspect-square rounded-lg border-2 mb-2 items-center justify-center ${
              data.naturalLevel === level.level
                ? 'border-purple-500 bg-purple-100'
                : 'border-gray-300 bg-white'
            }`}
          >
            <Text className="text-lg font-bold text-gray-800">{level.level}</Text>
            <Text className="text-xs text-gray-600 text-center">{level.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderUndertone = () => (
    <View className="mb-6">
      <View className="flex-row items-center mb-3">
        <Droplets size={20} color="#10B981" />
        <Text className="text-lg font-semibold text-gray-800 ml-2">
          Subtono Dominante
        </Text>
      </View>

      <View className="space-y-2">
        {undertones.map((tone) => (
          <TouchableOpacity
            key={tone.id}
            onPress={() => updateField('undertone', tone.id)}
            className={`p-3 rounded-lg border ${
              data.undertone === tone.id
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300 bg-white'
            }`}
          >
            <Text className="font-semibold text-gray-800">{tone.name}</Text>
            <Text className="text-sm text-gray-600">{tone.description}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderPorosity = () => (
    <View className="mb-6">
      <View className="flex-row items-center mb-3">
        <Droplets size={20} color="#F59E0B" />
        <Text className="text-lg font-semibold text-gray-800 ml-2">
          Porosidad del Cabello
        </Text>
      </View>

      <View className="space-y-2">
        {porosityLevels.map((level) => (
          <TouchableOpacity
            key={level.id}
            onPress={() => updateField('porosity', level.id)}
            className={`p-3 rounded-lg border ${
              data.porosity === level.id
                ? 'border-yellow-500 bg-yellow-50'
                : 'border-gray-300 bg-white'
            }`}
          >
            <Text className="font-semibold text-gray-800">{level.name}</Text>
            <Text className="text-sm text-gray-600">{level.description}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderChemicalHistory = () => (
    <View className="mb-6">
      <View className="flex-row items-center mb-3">
        <Clock size={20} color="#EF4444" />
        <Text className="text-lg font-semibold text-gray-800 ml-2">
          Historial Químico
        </Text>
      </View>

      <View className="bg-red-50 p-3 rounded-lg border border-red-200 mb-4">
        <Text className="text-red-800 font-medium mb-1">Importante:</Text>
        <Text className="text-red-700 text-sm">
          Selecciona todos los tratamientos aplicados en los últimos 12 meses
        </Text>
      </View>

      <View className="flex-row flex-wrap">
        {chemicalTreatments.map((treatment) => (
          <TouchableOpacity
            key={treatment}
            onPress={() => toggleChemicalTreatment(treatment)}
            className={`mr-2 mb-2 px-3 py-2 rounded-full border ${
              data.chemicalHistory.includes(treatment)
                ? 'border-red-500 bg-red-100'
                : 'border-gray-300 bg-white'
            }`}
          >
            <Text className={`text-sm ${
              data.chemicalHistory.includes(treatment)
                ? 'text-red-800 font-medium'
                : 'text-gray-700'
            }`}>
              {treatment}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <ScrollView className="flex-1 p-4">
      <View className="mb-6">
        <Text className="text-2xl font-bold text-gray-800 mb-2">
          Diagnóstico Capilar Exhaustivo
        </Text>
        <Text className="text-gray-600">
          Análisis completo para formulación precisa
        </Text>
      </View>

      {renderImageCapture()}
      {renderNaturalLevel()}
      {renderUndertone()}
      {renderPorosity()}
      {renderChemicalHistory()}

      {/* Resumen del diagnóstico */}
      {isComplete() && (
        <View className="bg-green-50 p-4 rounded-lg border border-green-200 mb-6">
          <View className="flex-row items-center mb-2">
            <CheckCircle size={20} color="#10B981" />
            <Text className="text-green-800 font-semibold ml-2">
              Diagnóstico Completo
            </Text>
          </View>
          <Text className="text-green-700 text-sm">
            • Nivel base: {data.naturalLevel}{'\n'}
            • Subtono: {undertones.find(t => t.id === data.undertone)?.name}{'\n'}
            • Porosidad: {porosityLevels.find(p => p.id === data.porosity)?.name}{'\n'}
            • Tratamientos previos: {data.chemicalHistory.length} registrados{'\n'}
            • Imágenes capturadas: {data.images.length}
          </Text>
        </View>
      )}

      {/* Botón de completar */}
      <TouchableOpacity
        onPress={onComplete}
        disabled={!isComplete()}
        className={`p-4 rounded-lg mt-6 ${
          isComplete()
            ? 'bg-blue-600'
            : 'bg-gray-300'
        }`}
      >
        <Text className={`text-center font-semibold ${
          isComplete() ? 'text-white' : 'text-gray-500'
        }`}>
          {isComplete() ? 'Continuar a Análisis de Color' : 'Completa todos los campos'}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default HairDiagnosisPhase;
