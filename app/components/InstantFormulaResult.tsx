import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Al<PERSON>,
  Animated,
  Share,
} from "react-native";
import {
  CheckCircle,
  Clock,
  DollarSign,
  AlertTriangle,
  Edit3,
  Share2,
  Save,
  Play,
  TestTube,
  Shield,
  TrendingUp,
  Star,
  Info,
  ChevronDown,
  ChevronUp,
} from "lucide-react-native";

interface Formula {
  id: string;
  product: string;
  brand: string;
  ratio: string;
  volume: string;
  time: number;
  confidence: number;
  cost: number;
  difficulty: "easy" | "medium" | "hard";
  instructions: string[];
  warnings?: string[];
  tips?: string[];
  aftercare?: string[];
}

interface InstantFormulaResultProps {
  formula: Formula;
  clientName?: string;
  goalDescription?: string;
  onApply?: () => void;
  onEdit?: () => void;
  onSave?: () => void;
  onShare?: () => void;
  showStrandTest?: boolean;
  analysisTime?: number;
}

const mockFormula: Formula = {
  id: "formula_001",
  product: "Koleston Perfect 8/3",
  brand: "Wella",
  ratio: "1:1.5",
  volume: "20vol",
  time: 35,
  confidence: 92,
  cost: 45,
  difficulty: "easy",
  instructions: [
    "Mezclar coloración con oxidante en proporción 1:1.5",
    "Aplicar primero en raíces, comenzando por la nuca",
    "Extender a medios y puntas en los últimos 10 minutos",
    "Controlar el proceso cada 10 minutos",
    "Aclarar con agua tibia hasta que salga clara",
    "Aplicar champú neutralizante y acondicionador"
  ],
  warnings: [
    "Realizar prueba de sensibilidad 48h antes",
    "No aplicar sobre cabello recién lavado",
    "Usar guantes durante todo el proceso"
  ],
  tips: [
    "Para mejor cobertura, aplicar en cabello seco",
    "Mantener temperatura ambiente entre 18-22°C",
    "Dividir el cabello en 4 secciones para aplicación uniforme"
  ],
  aftercare: [
    "Usar champú para cabello teñido",
    "Aplicar mascarilla nutritiva 1-2 veces por semana",
    "Evitar lavado frecuente los primeros 3 días",
    "Proteger del sol con productos con UV"
  ]
};

const InstantFormulaResult: React.FC<InstantFormulaResultProps> = ({
  formula = mockFormula,
  clientName = "Cliente",
  goalDescription = "Objetivo seleccionado",
  onApply = () => {},
  onEdit = () => {},
  onSave = () => {},
  onShare = () => {},
  showStrandTest = true,
  analysisTime = 0,
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [showInstructions, setShowInstructions] = useState(true);
  const [showWarnings, setShowWarnings] = useState(true);
  const [strandTestRequired, setStrandTestRequired] = useState(false);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Animación de entrada
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();

    // Determinar si se requiere prueba de mechón
    const requiresStrandTest = formula.difficulty === "hard" || 
                              formula.warnings?.some(w => w.includes("prueba")) ||
                              formula.confidence < 85;
    setStrandTestRequired(requiresStrandTest);
  }, []);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return "text-green-600 bg-green-100";
    if (confidence >= 80) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy": return "text-green-600 bg-green-100";
      case "medium": return "text-yellow-600 bg-yellow-100";
      case "hard": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const handleShare = async () => {
    try {
      const shareContent = `
Fórmula de Color - ${clientName}
${formula.brand} ${formula.product}
Proporción: ${formula.ratio} con ${formula.volume}
Tiempo: ${formula.time} minutos
Confianza: ${formula.confidence}%
Costo estimado: €${formula.cost}

Generado por Salonier AI
      `.trim();

      await Share.share({
        message: shareContent,
        title: `Fórmula para ${clientName}`,
      });
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  const handleApply = () => {
    if (strandTestRequired) {
      Alert.alert(
        "Prueba de Mechón Requerida",
        "Esta fórmula requiere una prueba de mechón antes de la aplicación completa. ¿Deseas programar la prueba?",
        [
          { text: "Cancelar", style: "cancel" },
          { text: "Programar Prueba", onPress: () => console.log("Programar prueba de mechón") },
          { text: "Aplicar Directamente", style: "destructive", onPress: onApply },
        ]
      );
    } else {
      Alert.alert(
        "Confirmar Aplicación",
        `¿Estás seguro de que quieres aplicar esta fórmula a ${clientName}?`,
        [
          { text: "Cancelar", style: "cancel" },
          { text: "Aplicar", onPress: onApply },
        ]
      );
    }
  };

  return (
    <Animated.View 
      style={{ 
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }]
      }}
      className="flex-1"
    >
      <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
        {/* Header de éxito */}
        <View className="bg-green-50 p-4 rounded-lg mb-4 border border-green-200">
          <View className="flex-row items-center mb-2">
            <CheckCircle size={24} color="#10B981" />
            <Text className="text-green-800 font-bold text-lg ml-2">
              ¡Fórmula Lista!
            </Text>
          </View>
          <Text className="text-green-700">
            Análisis completado en {analysisTime}s para {clientName}
          </Text>
          <Text className="text-green-600 text-sm mt-1">
            {goalDescription}
          </Text>
        </View>

        {/* Fórmula principal */}
        <View className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-4">
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-xl font-bold text-gray-800">Fórmula Recomendada</Text>
            <View className={`px-3 py-1 rounded-full ${getConfidenceColor(formula.confidence)}`}>
              <Text className="font-semibold text-sm">
                {formula.confidence}% confianza
              </Text>
            </View>
          </View>

          {/* Producto y mezcla */}
          <View className="bg-gray-50 p-4 rounded-lg mb-4">
            <Text className="font-bold text-gray-800 text-lg mb-2">
              {formula.brand} {formula.product}
            </Text>
            <View className="flex-row items-center">
              <Text className="text-gray-700 font-medium">
                Proporción: {formula.ratio} con {formula.volume}
              </Text>
            </View>
          </View>

          {/* Información clave */}
          <View className="flex-row justify-between mb-4">
            <View className="flex-1 items-center bg-blue-50 p-3 rounded-lg mr-2">
              <Clock size={20} color="#3B82F6" />
              <Text className="text-blue-800 font-semibold mt-1">{formula.time} min</Text>
              <Text className="text-blue-600 text-xs">Tiempo</Text>
            </View>
            
            <View className="flex-1 items-center bg-green-50 p-3 rounded-lg mx-1">
              <DollarSign size={20} color="#10B981" />
              <Text className="text-green-800 font-semibold mt-1">€{formula.cost}</Text>
              <Text className="text-green-600 text-xs">Costo</Text>
            </View>
            
            <View className="flex-1 items-center bg-purple-50 p-3 rounded-lg ml-2">
              <Star size={20} color="#8B5CF6" />
              <Text className="text-purple-800 font-semibold mt-1">
                {formula.difficulty === "easy" ? "Fácil" : 
                 formula.difficulty === "medium" ? "Medio" : "Avanzado"}
              </Text>
              <Text className="text-purple-600 text-xs">Dificultad</Text>
            </View>
          </View>

          {/* Alertas importantes */}
          {strandTestRequired && (
            <View className="bg-yellow-50 p-4 rounded-lg mb-4 border border-yellow-200">
              <View className="flex-row items-center mb-2">
                <TestTube size={20} color="#F59E0B" />
                <Text className="text-yellow-800 font-semibold ml-2">
                  Prueba de Mechón Recomendada
                </Text>
              </View>
              <Text className="text-yellow-700 text-sm">
                Esta fórmula requiere una prueba de mechón 24-48h antes de la aplicación completa.
              </Text>
            </View>
          )}

          {/* Advertencias */}
          {formula.warnings && formula.warnings.length > 0 && showWarnings && (
            <View className="bg-red-50 p-4 rounded-lg mb-4 border border-red-200">
              <TouchableOpacity 
                className="flex-row items-center justify-between mb-2"
                onPress={() => setShowWarnings(!showWarnings)}
              >
                <View className="flex-row items-center">
                  <AlertTriangle size={20} color="#EF4444" />
                  <Text className="text-red-800 font-semibold ml-2">Advertencias Importantes</Text>
                </View>
                {showWarnings ? <ChevronUp size={20} color="#EF4444" /> : <ChevronDown size={20} color="#EF4444" />}
              </TouchableOpacity>
              {showWarnings && formula.warnings.map((warning, index) => (
                <Text key={index} className="text-red-700 text-sm mb-1">
                  • {warning}
                </Text>
              ))}
            </View>
          )}
        </View>

        {/* Instrucciones paso a paso */}
        <View className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-4">
          <TouchableOpacity 
            className="flex-row items-center justify-between mb-4"
            onPress={() => setShowInstructions(!showInstructions)}
          >
            <Text className="text-lg font-bold text-gray-800">Instrucciones de Aplicación</Text>
            {showInstructions ? <ChevronUp size={24} color="#374151" /> : <ChevronDown size={24} color="#374151" />}
          </TouchableOpacity>
          
          {showInstructions && (
            <View>
              {formula.instructions.map((instruction, index) => (
                <View key={index} className="flex-row items-start mb-3">
                  <View className="w-6 h-6 bg-blue-500 rounded-full items-center justify-center mr-3 mt-0.5">
                    <Text className="text-white text-xs font-bold">{index + 1}</Text>
                  </View>
                  <Text className="flex-1 text-gray-700">{instruction}</Text>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Detalles adicionales */}
        <TouchableOpacity
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4"
          onPress={() => setShowDetails(!showDetails)}
        >
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Info size={20} color="#6B7280" />
              <Text className="text-gray-800 font-semibold ml-2">
                Tips Profesionales y Cuidados
              </Text>
            </View>
            {showDetails ? <ChevronUp size={20} color="#6B7280" /> : <ChevronDown size={20} color="#6B7280" />}
          </View>
          
          {showDetails && (
            <View className="mt-4">
              {/* Tips profesionales */}
              {formula.tips && formula.tips.length > 0 && (
                <View className="mb-4">
                  <Text className="font-semibold text-gray-800 mb-2">💡 Tips Profesionales:</Text>
                  {formula.tips.map((tip, index) => (
                    <Text key={index} className="text-gray-600 text-sm mb-1">
                      • {tip}
                    </Text>
                  ))}
                </View>
              )}

              {/* Cuidados posteriores */}
              {formula.aftercare && formula.aftercare.length > 0 && (
                <View>
                  <Text className="font-semibold text-gray-800 mb-2">🌟 Cuidados Posteriores:</Text>
                  {formula.aftercare.map((care, index) => (
                    <Text key={index} className="text-gray-600 text-sm mb-1">
                      • {care}
                    </Text>
                  ))}
                </View>
              )}
            </View>
          )}
        </TouchableOpacity>

        {/* Acciones rápidas */}
        <View className="space-y-3 mb-6">
          {/* Acción principal */}
          <TouchableOpacity
            className="bg-blue-500 p-4 rounded-lg flex-row items-center justify-center"
            onPress={handleApply}
          >
            <Play size={24} color="white" />
            <Text className="text-white font-bold text-lg ml-2">
              Aplicar Fórmula
            </Text>
          </TouchableOpacity>

          {/* Acciones secundarias */}
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 p-3 rounded-lg flex-row items-center justify-center"
              onPress={onEdit}
            >
              <Edit3 size={20} color="#374151" />
              <Text className="text-gray-800 font-semibold ml-2">Editar</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-1 bg-green-200 p-3 rounded-lg flex-row items-center justify-center"
              onPress={onSave}
            >
              <Save size={20} color="#059669" />
              <Text className="text-green-800 font-semibold ml-2">Guardar</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-1 bg-purple-200 p-3 rounded-lg flex-row items-center justify-center"
              onPress={handleShare}
            >
              <Share2 size={20} color="#7C3AED" />
              <Text className="text-purple-800 font-semibold ml-2">Compartir</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Footer informativo */}
        <View className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <View className="flex-row items-center mb-2">
            <Shield size={16} color="#3B82F6" />
            <Text className="text-blue-800 font-medium ml-2">Generado por Salonier AI</Text>
          </View>
          <Text className="text-blue-700 text-sm">
            Esta fórmula ha sido generada basándose en el análisis de imagen y tu experiencia profesional. 
            Siempre usa tu criterio profesional como decisión final.
          </Text>
        </View>

        {/* Espacio adicional para scroll */}
        <View className="h-20" />
      </ScrollView>
    </Animated.View>
  );
};

export default InstantFormulaResult;
