import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
} from "react-native";
import {
  ArrowLeft,
  Camera,
  Palette,
  FlaskConical,
  Shield,
  FileText,
  CheckCircle,
  AlertTriangle,
  Info,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import HairDiagnosisPhase from "./HairDiagnosisPhase";
import ColorAnalysisPhase from "./ColorAnalysisPhase";

// Tipos para el estado del proceso
type FormulationPhase = 'diagnosis' | 'color-analysis' | 'formulation' | 'documentation';

interface DiagnosisData {
  naturalLevel: number | null;
  undertone: string | null;
  porosity: string | null;
  chemicalHistory: string[];
  hairCondition: string | null;
  elasticity: string | null;
  images: string[];
  pH: string | null;
  texture: string | null;
  density: string | null;
}

interface ColorAnalysisData {
  currentColor: {
    level: number | null;
    undertone: string | null;
    reflections: string[];
  };
  desiredColor: {
    level: number | null;
    undertone: string | null;
    technique: string | null;
    referenceImages: string[];
    description: string | null;
  };
  viabilityAnalysis: {
    sessionsRequired: number;
    riskLevel: string;
    recommendations: string[];
    lightening: number;
    darkening: number;
  };
}

interface FormulationData {
  selectedBrand: string | null;
  productLine: string | null;
  formula: {
    colorants: Array<{ code: string; proportion: number }>;
    developer: { volume: number; ratio: string };
    additives: string[];
    processingTime: number;
  };
  safetyAlerts: string[];
}

const ProfessionalColorFormulation = () => {
  const router = useRouter();
  const [currentPhase, setCurrentPhase] = useState<FormulationPhase>('diagnosis');
  const [diagnosisData, setDiagnosisData] = useState<DiagnosisData>({
    naturalLevel: null,
    undertone: null,
    porosity: null,
    chemicalHistory: [],
    hairCondition: null,
    elasticity: null,
    images: [],
    pH: null,
    texture: null,
    density: null,
  });
  const [colorAnalysisData, setColorAnalysisData] = useState<ColorAnalysisData>({
    currentColor: { level: null, undertone: null, reflections: [] },
    desiredColor: { level: null, undertone: null, technique: null, referenceImages: [], description: null },
    viabilityAnalysis: { sessionsRequired: 1, riskLevel: 'low', recommendations: [], lightening: 0, darkening: 0 },
  });
  const [formulationData, setFormulationData] = useState<FormulationData>({
    selectedBrand: null,
    productLine: null,
    formula: {
      colorants: [],
      developer: { volume: 20, ratio: '1:1' },
      additives: [],
      processingTime: 30,
    },
    safetyAlerts: [],
  });

  const phases = [
    {
      id: 'diagnosis',
      title: 'Diagnóstico Capilar',
      subtitle: 'Análisis exhaustivo del cabello',
      icon: <Camera size={24} color="#fff" />,
      color: '#3B82F6',
      completed: diagnosisData.naturalLevel !== null,
    },
    {
      id: 'color-analysis',
      title: 'Análisis de Color',
      subtitle: 'Actual vs Deseado',
      icon: <Palette size={24} color="#fff" />,
      color: '#8B5CF6',
      completed: colorAnalysisData.desiredColor.level !== null,
    },
    {
      id: 'formulation',
      title: 'Formulación',
      subtitle: 'Cálculo preciso por marca',
      icon: <FlaskConical size={24} color="#fff" />,
      color: '#10B981',
      completed: formulationData.selectedBrand !== null,
    },
    {
      id: 'documentation',
      title: 'Documentación',
      subtitle: 'Registro del servicio',
      icon: <FileText size={24} color="#fff" />,
      color: '#F59E0B',
      completed: false,
    },
  ];

  const handlePhaseSelect = (phaseId: FormulationPhase) => {
    setCurrentPhase(phaseId);
  };

  const renderPhaseProgress = () => (
    <View className="px-4 py-6 bg-white border-b border-gray-200">
      <Text className="text-lg font-bold text-gray-800 mb-4">
        Proceso de Formulación Profesional
      </Text>
      
      <View className="flex-row justify-between">
        {phases.map((phase, index) => (
          <TouchableOpacity
            key={phase.id}
            onPress={() => handlePhaseSelect(phase.id as FormulationPhase)}
            className="items-center flex-1"
          >
            <View
              style={{ backgroundColor: currentPhase === phase.id ? phase.color : '#E5E7EB' }}
              className="w-12 h-12 rounded-full items-center justify-center mb-2"
            >
              {phase.completed ? (
                <CheckCircle size={24} color="#fff" />
              ) : (
                phase.icon
              )}
            </View>
            <Text
              className={`text-xs text-center font-medium ${
                currentPhase === phase.id ? 'text-gray-800' : 'text-gray-500'
              }`}
            >
              {phase.title}
            </Text>
            <Text className="text-xs text-gray-400 text-center">
              {phase.subtitle}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const handlePhaseComplete = (phase: FormulationPhase) => {
    // Avanzar a la siguiente fase automáticamente
    const phaseOrder: FormulationPhase[] = ['diagnosis', 'color-analysis', 'formulation', 'documentation'];
    const currentIndex = phaseOrder.indexOf(phase);
    if (currentIndex < phaseOrder.length - 1) {
      setCurrentPhase(phaseOrder[currentIndex + 1]);
    }
  };

  const renderCurrentPhase = () => {
    switch (currentPhase) {
      case 'diagnosis':
        return (
          <HairDiagnosisPhase
            data={diagnosisData}
            onUpdate={setDiagnosisData}
            onComplete={() => handlePhaseComplete('diagnosis')}
          />
        );
      case 'color-analysis':
        return (
          <ColorAnalysisPhase
            data={colorAnalysisData}
            onUpdate={setColorAnalysisData}
            onComplete={() => handlePhaseComplete('color-analysis')}
            diagnosisData={diagnosisData}
          />
        );
      case 'formulation':
        return <FormulationPhase data={formulationData} onUpdate={setFormulationData} />;
      case 'documentation':
        return <DocumentationPhase />;
      default:
        return (
          <HairDiagnosisPhase
            data={diagnosisData}
            onUpdate={setDiagnosisData}
            onComplete={() => handlePhaseComplete('diagnosis')}
          />
        );
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#F9FAFB" />
      
      {/* Header */}
      <View className="bg-white px-4 py-3 shadow-sm border-b border-gray-100">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity
            onPress={() => router.back()}
            className="flex-row items-center"
          >
            <ArrowLeft size={24} color="#374151" />
            <Text className="text-gray-800 font-semibold ml-2">Volver</Text>
          </TouchableOpacity>
          
          <View className="bg-blue-100 px-3 py-1 rounded-full">
            <Text className="text-blue-800 font-semibold text-sm">
              🧪 FORMULACIÓN PRO
            </Text>
          </View>
        </View>
      </View>

      {/* Progress Indicator */}
      {renderPhaseProgress()}

      {/* Current Phase Content */}
      <ScrollView className="flex-1">
        {renderCurrentPhase()}
      </ScrollView>
    </SafeAreaView>
  );
};

// Placeholder components for remaining phases

const ColorAnalysisPhase = ({ data, onUpdate }: { data: ColorAnalysisData; onUpdate: (data: ColorAnalysisData) => void }) => (
  <View className="p-4">
    <Text className="text-xl font-bold text-gray-800 mb-4">
      Análisis de Color Actual vs Deseado
    </Text>
    <View className="bg-purple-50 p-4 rounded-lg border border-purple-200">
      <Text className="text-purple-800 font-semibold mb-2">Fase 2: Definición del Objetivo</Text>
      <Text className="text-purple-700">
        Análisis del color actual, definición del color deseado y evaluación de viabilidad del cambio.
      </Text>
    </View>
  </View>
);

const FormulationPhase = ({ data, onUpdate }: { data: FormulationData; onUpdate: (data: FormulationData) => void }) => (
  <View className="p-4">
    <Text className="text-xl font-bold text-gray-800 mb-4">
      Formulación Experta por Marca
    </Text>
    <View className="bg-green-50 p-4 rounded-lg border border-green-200">
      <Text className="text-green-800 font-semibold mb-2">Fase 3: Cálculo de Fórmula</Text>
      <Text className="text-green-700">
        Selección de marca, línea de productos, cálculo de proporciones y validaciones de seguridad.
      </Text>
    </View>
  </View>
);

const DocumentationPhase = () => (
  <View className="p-4">
    <Text className="text-xl font-bold text-gray-800 mb-4">
      Documentación del Servicio
    </Text>
    <View className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
      <Text className="text-yellow-800 font-semibold mb-2">Fase 4: Registro Completo</Text>
      <Text className="text-yellow-700">
        Documentación del resultado, fórmula aplicada, notas del estilista y vinculación al historial.
      </Text>
    </View>
  </View>
);

export default ProfessionalColorFormulation;
