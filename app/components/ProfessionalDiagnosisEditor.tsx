import React, { useState } from "react";
import { View, Text, ScrollView, TouchableOpacity, TextInput, Alert } from "react-native";
import { X, Save, CheckCircle, AlertTriangle, Clock, User, TestTube, History, Settings } from "lucide-react-native";

interface ProfessionalDiagnosisEditorProps {
  visible: boolean;
  diagnosisData: any;
  onSave: (updatedData: any) => void;
  onClose: () => void;
}

const ProfessionalDiagnosisEditor = ({
  visible,
  diagnosisData,
  onSave,
  onClose,
}: ProfessionalDiagnosisEditorProps) => {
  const [activeTab, setActiveTab] = useState(0);
  const [editedData, setEditedData] = useState(diagnosisData || {});
  const [completionStatus, setCompletionStatus] = useState({
    basicInfo: false,
    physicalTests: false,
    chemicalHistory: false,
    clientInfo: false,
    verification: false,
  });

  const tabs = [
    { id: 0, title: "Básico", icon: CheckCircle, key: "basicInfo" },
    { id: 1, title: "Tests", icon: TestTube, key: "physicalTests" },
    { id: 2, title: "Historial", icon: History, key: "chemicalHistory" },
    { id: 3, title: "Cliente", icon: User, key: "clientInfo" },
    { id: 4, title: "Verificar", icon: Settings, key: "verification" },
  ];

  const updateField = (path: string, value: any) => {
    const keys = path.split('.');
    const newData = { ...editedData };
    let current = newData;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) current[keys[i]] = {};
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    setEditedData(newData);
    
    // Update completion status
    updateCompletionStatus(newData);
  };

  const getFieldValue = (path: string) => {
    const keys = path.split('.');
    let current = editedData;
    
    for (const key of keys) {
      if (!current || current[key] === undefined) return '';
      current = current[key];
    }
    
    return current;
  };

  const updateCompletionStatus = (data: any) => {
    const status = {
      basicInfo: !!(data.naturalLevel && data.undertone && data.condition),
      physicalTests: !!(data.physicalTests?.porosityTest?.performed && 
                       data.physicalTests?.elasticityTest?.performed),
      chemicalHistory: !!(data.chemicalHistory?.lastColorService?.date),
      clientInfo: !!(data.clientInfo?.scalpCondition && 
                    data.clientInfo?.pregnancyStatus),
      verification: !!(data.verification?.stylistReviewComplete),
    };
    setCompletionStatus(status);
  };

  const renderTabIndicator = () => (
    <View className="flex-row bg-gray-100 rounded-lg p-1 mb-4">
      {tabs.map((tab) => {
        const IconComponent = tab.icon;
        const isComplete = completionStatus[tab.key];
        const isActive = activeTab === tab.id;
        
        return (
          <TouchableOpacity
            key={tab.id}
            onPress={() => setActiveTab(tab.id)}
            className={`flex-1 flex-row items-center justify-center py-2 px-1 rounded-md ${
              isActive ? "bg-purple-500" : "bg-transparent"
            }`}
          >
            <IconComponent 
              size={16} 
              color={isActive ? "white" : isComplete ? "#10B981" : "#6B7280"} 
            />
            <Text className={`ml-1 text-xs font-medium ${
              isActive ? "text-white" : isComplete ? "text-green-600" : "text-gray-600"
            }`}>
              {tab.title}
            </Text>
            {isComplete && !isActive && (
              <View className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full" />
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );

  const renderSelect = (label: string, path: string, options: string[], required = false) => (
    <View className="mb-4">
      <Text className={`font-semibold mb-2 ${required ? 'text-red-600' : 'text-gray-700'}`}>
        {label} {required && '*'}
      </Text>
      <View className="flex-row flex-wrap">
        {options.map((option) => (
          <TouchableOpacity
            key={option}
            onPress={() => updateField(path, option)}
            className={`px-3 py-2 rounded-full mr-2 mb-2 ${
              getFieldValue(path) === option
                ? "bg-purple-500"
                : "bg-gray-200"
            }`}
          >
            <Text
              className={`text-sm ${
                getFieldValue(path) === option
                  ? "text-white font-semibold"
                  : "text-gray-700"
              }`}
            >
              {option}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderSlider = (label: string, path: string, min: number, max: number, required = false) => (
    <View className="mb-4">
      <Text className={`font-semibold mb-2 ${required ? 'text-red-600' : 'text-gray-700'}`}>
        {label} {required && '*'}
      </Text>
      <View className="flex-row items-center">
        <Text className="text-gray-500 mr-2">{min}</Text>
        <View className="flex-1 h-6 bg-gray-200 rounded-full mx-2">
          <TouchableOpacity
            className="h-6 bg-purple-500 rounded-full flex items-center justify-center"
            style={{ width: `${((getFieldValue(path) - min) / (max - min)) * 100}%` }}
          >
            <View className="w-4 h-4 bg-white rounded-full" />
          </TouchableOpacity>
        </View>
        <Text className="text-gray-500 ml-2">{max}</Text>
        <Text className="font-bold ml-3 w-8">{getFieldValue(path) || min}</Text>
      </View>
    </View>
  );

  const renderTextInput = (label: string, path: string, placeholder?: string, required = false, multiline = false) => (
    <View className="mb-4">
      <Text className={`font-semibold mb-2 ${required ? 'text-red-600' : 'text-gray-700'}`}>
        {label} {required && '*'}
      </Text>
      <TextInput
        className={`border border-gray-300 rounded-lg p-3 bg-white ${multiline ? 'min-h-[80px]' : ''}`}
        value={getFieldValue(path)?.toString() || ''}
        onChangeText={(text) => updateField(path, text)}
        placeholder={placeholder}
        multiline={multiline}
        textAlignVertical={multiline ? "top" : "center"}
      />
    </View>
  );

  const renderPhysicalTestGuide = (testType: string) => {
    const guides = {
      porosity: {
        title: "Test de Porosidad",
        steps: [
          "Toma un mechón limpio y seco",
          "Sumérgelo en un vaso de agua",
          "Observa el comportamiento:",
          "• Flota = Porosidad Baja",
          "• Se hunde lentamente = Porosidad Media", 
          "• Se hunde rápido = Porosidad Alta"
        ]
      },
      elasticity: {
        title: "Test de Elasticidad",
        steps: [
          "Toma un cabello húmedo",
          "Estíralo suavemente",
          "Observa el resultado:",
          "• Vuelve a su forma = Excelente",
          "• Ligera deformación = Buena",
          "• Se estira mucho = Pobre",
          "• Se rompe fácil = Muy Pobre"
        ]
      },
      density: {
        title: "Test de Densidad",
        steps: [
          "Haz una raya de 2.5cm",
          "Observa el cuero cabelludo:",
          "• Muy visible = Densidad Baja",
          "• Parcialmente visible = Media",
          "• Apenas visible = Alta"
        ]
      }
    };

    const guide = guides[testType];
    if (!guide) return null;

    return (
      <View className="bg-blue-50 p-3 rounded-lg mb-4">
        <Text className="font-bold text-blue-800 mb-2">{guide.title}</Text>
        {guide.steps.map((step, index) => (
          <Text key={index} className="text-blue-700 text-sm py-0.5">
            {step}
          </Text>
        ))}
      </View>
    );
  };

  if (!visible) return null;

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white border-b border-gray-200 px-4 py-3">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={onClose}>
            <X size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-800">Editor Profesional</Text>
          <TouchableOpacity
            onPress={() => {
              // Validate required fields
              const allComplete = Object.values(completionStatus).every(status => status);
              if (!allComplete) {
                Alert.alert(
                  "Información Incompleta",
                  "Por favor completa todas las secciones obligatorias antes de guardar.",
                  [{ text: "Continuar Editando" }]
                );
                return;
              }
              
              // Mark as stylist reviewed
              const finalData = {
                ...editedData,
                verification: {
                  ...editedData.verification,
                  stylistReviewComplete: true,
                  readyForFormulation: true,
                  lastModifiedBy: "stylist",
                  lastModified: new Date().toISOString(),
                }
              };
              
              onSave(finalData);
              onClose();
            }}
            className="bg-purple-500 px-4 py-2 rounded-lg"
          >
            <Text className="text-white font-semibold">Guardar</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Progress Indicator */}
      <View className="px-4 py-3 bg-white border-b border-gray-100">
        <View className="flex-row items-center justify-between mb-2">
          <Text className="text-sm font-medium text-gray-600">Progreso de Verificación</Text>
          <Text className="text-sm font-bold text-purple-600">
            {Object.values(completionStatus).filter(Boolean).length}/5
          </Text>
        </View>
        <View className="w-full h-2 bg-gray-200 rounded-full">
          <View 
            className="h-2 bg-purple-500 rounded-full"
            style={{ 
              width: `${(Object.values(completionStatus).filter(Boolean).length / 5) * 100}%` 
            }}
          />
        </View>
      </View>

      {/* Tab Navigation */}
      <View className="px-4 py-3 bg-white">
        {renderTabIndicator()}
      </View>

      {/* Tab Content */}
      <ScrollView className="flex-1 px-4 py-2">
        {/* Content will be rendered based on activeTab */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-bold mb-4 text-purple-800">
            {tabs[activeTab].title} - Información {activeTab === 0 ? "Básica" : 
             activeTab === 1 ? "de Tests Físicos" :
             activeTab === 2 ? "del Historial Químico" :
             activeTab === 3 ? "del Cliente" : "de Verificación"}
          </Text>
          
          {/* Tab 0: Información Básica */}
          {activeTab === 0 && (
            <>
              <View className="bg-yellow-50 p-3 rounded-lg mb-4">
                <Text className="text-yellow-800 font-medium mb-1">📋 Verificación de IA</Text>
                <Text className="text-yellow-700 text-sm">
                  Revisa y ajusta la información detectada por la IA. Tu criterio profesional es definitivo.
                </Text>
              </View>

              {renderSlider("Nivel Natural", "naturalLevel", 1, 10, true)}
              {renderSelect("Subtono", "undertone", ["warm", "cool", "neutral"], true)}
              {renderSelect("Condición General", "condition", ["healthy", "normal", "damaged", "severely_damaged"], true)}

              <Text className="font-bold text-gray-700 mb-3 mt-6">Análisis por Zonas</Text>

              <Text className="font-semibold text-purple-700 mb-2">Raíces</Text>
              {renderSlider("Nivel Natural Raíces", "zoneAnalysis.roots.naturalLevel", 1, 10)}
              {renderSelect("Condición Raíces", "zoneAnalysis.roots.condition", ["virgin", "regrowth", "previously_treated"])}
              {renderSlider("% Canas en Raíces", "zoneAnalysis.roots.grayPercentage", 0, 100)}

              <Text className="font-semibold text-purple-700 mb-2 mt-4">Medios</Text>
              {renderSlider("Nivel Actual Medios", "zoneAnalysis.midLengths.level", 1, 12)}
              {renderSelect("Condición Medios", "zoneAnalysis.midLengths.condition", ["healthy", "normal", "damaged", "severely_damaged"])}

              <Text className="font-semibold text-purple-700 mb-2 mt-4">Puntas</Text>
              {renderSlider("Nivel Actual Puntas", "zoneAnalysis.ends.level", 1, 12)}
              {renderSelect("Condición Puntas", "zoneAnalysis.ends.condition", ["healthy", "normal", "damaged", "severely_damaged"])}
            </>
          )}

          {/* Tab 1: Tests Físicos */}
          {activeTab === 1 && (
            <>
              <View className="bg-red-50 p-3 rounded-lg mb-4">
                <Text className="text-red-800 font-medium mb-1">🧪 Tests Obligatorios</Text>
                <Text className="text-red-700 text-sm">
                  Realiza estos tests físicos para verificar las propiedades del cabello. Son críticos para la formulación.
                </Text>
              </View>

              {/* Test de Porosidad */}
              <View className="mb-6">
                <Text className="font-bold text-gray-700 mb-3">Test de Porosidad</Text>
                {renderPhysicalTestGuide("porosity")}

                <View className="mb-3">
                  <Text className="font-semibold mb-2 text-red-600">¿Realizaste el test? *</Text>
                  <View className="flex-row">
                    <TouchableOpacity
                      onPress={() => updateField("physicalTests.porosityTest.performed", true)}
                      className={`px-4 py-2 rounded-lg mr-2 ${
                        getFieldValue("physicalTests.porosityTest.performed")
                          ? "bg-green-500"
                          : "bg-gray-200"
                      }`}
                    >
                      <Text className={getFieldValue("physicalTests.porosityTest.performed") ? "text-white" : "text-gray-700"}>
                        Sí, realizado
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => updateField("physicalTests.porosityTest.performed", false)}
                      className={`px-4 py-2 rounded-lg ${
                        !getFieldValue("physicalTests.porosityTest.performed")
                          ? "bg-red-500"
                          : "bg-gray-200"
                      }`}
                    >
                      <Text className={!getFieldValue("physicalTests.porosityTest.performed") ? "text-white" : "text-gray-700"}>
                        No realizado
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {getFieldValue("physicalTests.porosityTest.performed") && (
                  <>
                    {renderSelect("Método Usado", "physicalTests.porosityTest.method", ["water_test", "strand_test", "visual_assessment"])}
                    {renderSelect("Resultado del Test", "physicalTests.porosityTest.result", ["low", "medium", "high"], true)}
                    {renderTextInput("Notas del Test", "physicalTests.porosityTest.notes", "Observaciones específicas del test")}
                  </>
                )}
              </View>

              {/* Test de Elasticidad */}
              <View className="mb-6">
                <Text className="font-bold text-gray-700 mb-3">Test de Elasticidad</Text>
                {renderPhysicalTestGuide("elasticity")}

                <View className="mb-3">
                  <Text className="font-semibold mb-2 text-red-600">¿Realizaste el test? *</Text>
                  <View className="flex-row">
                    <TouchableOpacity
                      onPress={() => updateField("physicalTests.elasticityTest.performed", true)}
                      className={`px-4 py-2 rounded-lg mr-2 ${
                        getFieldValue("physicalTests.elasticityTest.performed")
                          ? "bg-green-500"
                          : "bg-gray-200"
                      }`}
                    >
                      <Text className={getFieldValue("physicalTests.elasticityTest.performed") ? "text-white" : "text-gray-700"}>
                        Sí, realizado
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {getFieldValue("physicalTests.elasticityTest.performed") && (
                  <>
                    {renderSelect("Resultado", "physicalTests.elasticityTest.result", ["excellent", "good", "poor", "very_poor"], true)}
                    {renderTextInput("Notas", "physicalTests.elasticityTest.notes", "Observaciones del test")}
                  </>
                )}
              </View>
            </>
          )}

          {/* Tab 2: Historial Químico */}
          {activeTab === 2 && (
            <>
              <View className="bg-orange-50 p-3 rounded-lg mb-4">
                <Text className="text-orange-800 font-medium mb-1">📅 Historial Crítico</Text>
                <Text className="text-orange-700 text-sm">
                  Esta información es fundamental para evitar reacciones y calcular tiempos correctos.
                </Text>
              </View>

              <Text className="font-bold text-gray-700 mb-3">Último Servicio de Color</Text>
              {renderSelect("Tipo de Servicio", "chemicalHistory.lastColorService.type",
                ["permanent", "semi_permanent", "bleach", "highlights", "balayage", "ombre", "none"], true)}

              {renderTextInput("Fecha Exacta", "chemicalHistory.lastColorService.date", "DD/MM/YYYY", true)}
              {renderTextInput("Marca Utilizada", "chemicalHistory.lastColorService.brand", "Ej: L'Oréal, Wella, Matrix", true)}
              {renderTextInput("Tono/Shade", "chemicalHistory.lastColorService.shade", "Ej: 7.1, 8N, etc.")}
              {renderSelect("Oxidante Usado", "chemicalHistory.lastColorService.developer", ["10vol", "20vol", "30vol", "40vol"])}

              <Text className="font-bold text-gray-700 mb-3 mt-6">Tratamientos Previos</Text>
              {renderTextInput("Último Servicio Químico", "chemicalHistory.lastChemicalService", "Fecha del último químico", true)}
              {renderSelect("Nivel de Daño Químico", "chemicalHistory.chemicalDamage", ["none", "minimal", "moderate", "severe"], true)}

              <View className="bg-blue-50 p-3 rounded-lg mt-4">
                <Text className="text-blue-800 font-medium mb-1">💡 Tip Profesional</Text>
                <Text className="text-blue-700 text-sm">
                  Servicios químicos recientes (menos de 2 semanas) requieren precaución extra y posibles pretratamientos.
                </Text>
              </View>
            </>
          )}

          {/* Tab 3: Información del Cliente */}
          {activeTab === 3 && (
            <>
              <View className="bg-red-50 p-3 rounded-lg mb-4">
                <Text className="text-red-800 font-medium mb-1">⚠️ Información Crítica</Text>
                <Text className="text-red-700 text-sm">
                  Esta información es obligatoria por seguridad y responsabilidad profesional.
                </Text>
              </View>

              <Text className="font-bold text-gray-700 mb-3">Salud y Seguridad</Text>
              {renderSelect("Estado del Cuero Cabelludo", "clientInfo.scalpCondition",
                ["healthy", "sensitive", "irritated", "psoriasis", "eczema", "other"], true)}

              {renderSelect("Estado de Embarazo", "clientInfo.pregnancyStatus",
                ["not_pregnant", "pregnant", "breastfeeding", "unknown"], true)}

              {renderTextInput("Alergias Conocidas", "clientInfo.allergies", "Separar con comas", true, true)}
              {renderTextInput("Medicamentos Actuales", "clientInfo.medications", "Incluir suplementos", false, true)}
              {renderTextInput("Reacciones Previas", "clientInfo.previousReactions", "Describe cualquier reacción anterior", false, true)}

              <Text className="font-bold text-gray-700 mb-3 mt-6">Estilo de Vida</Text>
              {renderSelect("Frecuencia de Lavado", "clientInfo.lifestyle.washFrequency",
                ["daily", "every_other_day", "twice_weekly", "weekly", "less_than_weekly"])}

              {renderSelect("Uso de Calor", "clientInfo.lifestyle.heatStyling",
                ["daily", "frequent", "occasional", "never"])}

              {renderSelect("Exposición al Sol", "clientInfo.lifestyle.sunExposure",
                ["high", "moderate", "low"])}

              <Text className="font-bold text-gray-700 mb-3 mt-6">Expectativas del Servicio</Text>
              {renderTextInput("Tiempo Disponible (min)", "serviceInfo.timeAvailable", "Ej: 180", true)}
              {renderTextInput("Presupuesto Aproximado (€)", "serviceInfo.budget", "Ej: 150")}
              {renderSelect("Tolerancia al Riesgo", "serviceInfo.riskTolerance",
                ["conservative", "moderate", "adventurous"])}
            </>
          )}

          {/* Tab 4: Verificación Final */}
          {activeTab === 4 && (
            <>
              <View className="bg-green-50 p-3 rounded-lg mb-4">
                <Text className="text-green-800 font-medium mb-1">✅ Verificación Final</Text>
                <Text className="text-green-700 text-sm">
                  Confirma que toda la información está completa y es correcta antes de proceder.
                </Text>
              </View>

              <View className="space-y-3">
                {Object.entries(completionStatus).map(([key, isComplete]) => {
                  const labels = {
                    basicInfo: "Información Básica",
                    physicalTests: "Tests Físicos",
                    chemicalHistory: "Historial Químico",
                    clientInfo: "Información del Cliente",
                    verification: "Verificación del Estilista"
                  };

                  return (
                    <View key={key} className="flex-row items-center justify-between p-3 bg-white rounded-lg border">
                      <Text className="font-medium text-gray-700">{labels[key]}</Text>
                      <View className={`w-6 h-6 rounded-full ${isComplete ? 'bg-green-500' : 'bg-red-500'} items-center justify-center`}>
                        <Text className="text-white text-xs font-bold">{isComplete ? '✓' : '✗'}</Text>
                      </View>
                    </View>
                  );
                })}
              </View>

              <View className="mt-6">
                <TouchableOpacity
                  onPress={() => updateField("verification.stylistReviewComplete", true)}
                  className={`p-4 rounded-lg ${
                    getFieldValue("verification.stylistReviewComplete")
                      ? "bg-green-500"
                      : "bg-gray-300"
                  }`}
                >
                  <Text className={`text-center font-bold ${
                    getFieldValue("verification.stylistReviewComplete")
                      ? "text-white"
                      : "text-gray-600"
                  }`}>
                    {getFieldValue("verification.stylistReviewComplete")
                      ? "✅ Verificado por Estilista"
                      : "Marcar como Verificado"}
                  </Text>
                </TouchableOpacity>
              </View>

              {getFieldValue("verification.stylistReviewComplete") && (
                <View className="bg-purple-50 p-4 rounded-lg mt-4">
                  <Text className="text-purple-800 font-medium mb-2">🎯 Listo para Formulación</Text>
                  <Text className="text-purple-700 text-sm">
                    El diagnóstico ha sido completado y verificado. Puedes proceder con confianza a generar la formulación profesional.
                  </Text>
                </View>
              )}
            </>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default ProfessionalDiagnosisEditor;
