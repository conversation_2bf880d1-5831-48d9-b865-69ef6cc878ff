import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Animated,
  Dimensions,
} from "react-native";
import {
  Star,
  Sparkles,
  Flame,
  Zap,
  CheckCircle,
  Rainbow,
  <PERSON><PERSON>,
  Clock,
  DollarSign,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON>dingUp,
  <PERSON>,
} from "lucide-react-native";

const { width } = Dimensions.get("window");

interface Goal {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  description: string;
  difficulty: "easy" | "medium" | "hard";
  timeEstimate: number; // minutes
  costRange: string;
  popularity: number; // 1-5
  riskLevel: "low" | "medium" | "high";
  tips: string[];
  contraindications?: string[];
}

interface QuickGoalSelectorProps {
  onSelect: (goal: Goal) => void;
  selectedGoal?: Goal | null;
  showDetails?: boolean;
  maxColumns?: number;
}

const colorGoals: Goal[] = [
  {
    id: "cover_gray",
    label: "<PERSON><PERSON><PERSON><PERSON>",
    icon: Star,
    color: "#F59E0B",
    bgColor: "bg-yellow-500",
    description: "Cobertura completa y natural de canas",
    difficulty: "easy",
    timeEstimate: 35,
    costRange: "€35-50",
    popularity: 5,
    riskLevel: "low",
    tips: [
      "Usar coloración permanente para mejor cobertura",
      "Aplicar primero en zonas con más canas",
      "Tiempo de pose según porcentaje de canas"
    ],
  },
  {
    id: "subtle_change",
    label: "Cambio Sutil",
    icon: Sparkles,
    color: "#3B82F6",
    bgColor: "bg-blue-500",
    description: "Mejora natural del color existente",
    difficulty: "easy",
    timeEstimate: 30,
    costRange: "€30-45",
    popularity: 4,
    riskLevel: "low",
    tips: [
      "Máximo 1-2 tonos de diferencia",
      "Usar tonalizantes o semi-permanentes",
      "Ideal para realzar el color natural"
    ],
  },
  {
    id: "dramatic",
    label: "Cambio Dramático",
    icon: Flame,
    color: "#EF4444",
    bgColor: "bg-red-500",
    description: "Transformación completa del color",
    difficulty: "hard",
    timeEstimate: 90,
    costRange: "€60-120",
    popularity: 3,
    riskLevel: "high",
    tips: [
      "Requiere decoloración previa",
      "Proceso en múltiples sesiones",
      "Prueba de mechón obligatoria"
    ],
    contraindications: [
      "Cabello muy dañado",
      "Tratamientos químicos recientes",
      "Embarazo"
    ],
  },
  {
    id: "highlights",
    label: "Reflejos",
    icon: Zap,
    color: "#8B5CF6",
    bgColor: "bg-purple-500",
    description: "Mechas y reflejos naturales",
    difficulty: "medium",
    timeEstimate: 60,
    costRange: "€45-80",
    popularity: 5,
    riskLevel: "medium",
    tips: [
      "Técnica de papel aluminio o gorro",
      "Seleccionar mechones estratégicamente",
      "Tono 2-3 niveles más claro que la base"
    ],
  },
  {
    id: "maintenance",
    label: "Mantenimiento",
    icon: CheckCircle,
    color: "#10B981",
    bgColor: "bg-green-500",
    description: "Retocar raíces y refrescar color",
    difficulty: "easy",
    timeEstimate: 25,
    costRange: "€25-40",
    popularity: 5,
    riskLevel: "low",
    tips: [
      "Solo aplicar en raíces nuevas",
      "Usar la misma fórmula anterior",
      "Tiempo de pose reducido"
    ],
  },
  {
    id: "trending",
    label: "Color Tendencia",
    icon: Rainbow,
    color: "#EC4899",
    bgColor: "bg-pink-500",
    description: "Colores de moda y tendencias actuales",
    difficulty: "medium",
    timeEstimate: 75,
    costRange: "€50-90",
    popularity: 4,
    riskLevel: "medium",
    tips: [
      "Consultar tendencias actuales",
      "Considerar tono de piel",
      "Mantenimiento frecuente requerido"
    ],
  },
];

const QuickGoalSelector: React.FC<QuickGoalSelectorProps> = ({
  onSelect,
  selectedGoal,
  showDetails = true,
  maxColumns = 2,
}) => {
  const [expandedGoal, setExpandedGoal] = useState<string | null>(null);
  const [showAllGoals, setShowAllGoals] = useState(false);
  const scaleAnims = useRef(
    colorGoals.reduce((acc, goal) => {
      acc[goal.id] = new Animated.Value(1);
      return acc;
    }, {} as Record<string, Animated.Value>)
  ).current;

  const popularGoals = colorGoals
    .filter(goal => goal.popularity >= 4)
    .sort((a, b) => b.popularity - a.popularity);

  const displayGoals = showAllGoals ? colorGoals : popularGoals;

  const handleGoalPress = (goal: Goal) => {
    // Animación de selección
    Animated.sequence([
      Animated.timing(scaleAnims[goal.id], {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnims[goal.id], {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    if (expandedGoal === goal.id) {
      // Si ya está expandido, seleccionar
      onSelect(goal);
    } else {
      // Expandir para mostrar detalles
      setExpandedGoal(goal.id);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy": return "text-green-600 bg-green-100";
      case "medium": return "text-yellow-600 bg-yellow-100";
      case "hard": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low": return "text-green-600";
      case "medium": return "text-yellow-600";
      case "high": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  const renderGoalCard = (goal: Goal) => {
    const IconComponent = goal.icon;
    const isExpanded = expandedGoal === goal.id;
    const isSelected = selectedGoal?.id === goal.id;

    return (
      <Animated.View
        key={goal.id}
        style={{ transform: [{ scale: scaleAnims[goal.id] }] }}
        className={`mb-4 ${maxColumns === 2 ? 'w-[48%]' : 'w-full'}`}
      >
        <TouchableOpacity
          className={`bg-white rounded-lg shadow-sm border-2 overflow-hidden ${
            isSelected ? 'border-blue-500' : 'border-gray-100'
          }`}
          onPress={() => handleGoalPress(goal)}
        >
          {/* Header del card */}
          <View className="p-4">
            <View className="flex-row items-center mb-2">
              <View className={`w-12 h-12 ${goal.bgColor} rounded-full items-center justify-center mr-3`}>
                <IconComponent size={24} color="white" />
              </View>
              <View className="flex-1">
                <Text className="font-semibold text-gray-800">{goal.label}</Text>
                <View className="flex-row items-center mt-1">
                  {[...Array(goal.popularity)].map((_, i) => (
                    <Star key={i} size={12} color="#F59E0B" fill="#F59E0B" />
                  ))}
                  <Text className="text-xs text-gray-500 ml-1">Popular</Text>
                </View>
              </View>
            </View>

            <Text className="text-gray-600 text-sm mb-3">{goal.description}</Text>

            {/* Información rápida */}
            <View className="flex-row justify-between items-center">
              <View className="flex-row items-center">
                <Clock size={14} color="#6B7280" />
                <Text className="text-xs text-gray-600 ml-1">{goal.timeEstimate}min</Text>
              </View>
              <View className="flex-row items-center">
                <DollarSign size={14} color="#6B7280" />
                <Text className="text-xs text-gray-600 ml-1">{goal.costRange}</Text>
              </View>
              <View className={`px-2 py-1 rounded-full ${getDifficultyColor(goal.difficulty)}`}>
                <Text className="text-xs font-medium">
                  {goal.difficulty === "easy" ? "Fácil" : 
                   goal.difficulty === "medium" ? "Medio" : "Avanzado"}
                </Text>
              </View>
            </View>
          </View>

          {/* Detalles expandidos */}
          {isExpanded && showDetails && (
            <View className="border-t border-gray-100 p-4 bg-gray-50">
              {/* Nivel de riesgo */}
              <View className="flex-row items-center mb-3">
                <AlertTriangle size={16} color={getRiskColor(goal.riskLevel).includes('green') ? '#10B981' : 
                                                  getRiskColor(goal.riskLevel).includes('yellow') ? '#F59E0B' : '#EF4444'} />
                <Text className="text-sm font-medium text-gray-700 ml-2">
                  Riesgo: <Text className={getRiskColor(goal.riskLevel)}>
                    {goal.riskLevel === "low" ? "Bajo" : 
                     goal.riskLevel === "medium" ? "Medio" : "Alto"}
                  </Text>
                </Text>
              </View>

              {/* Tips profesionales */}
              <Text className="text-sm font-medium text-gray-700 mb-2">💡 Tips Profesionales:</Text>
              {goal.tips.map((tip, index) => (
                <Text key={index} className="text-sm text-gray-600 mb-1">
                  • {tip}
                </Text>
              ))}

              {/* Contraindicaciones */}
              {goal.contraindications && goal.contraindications.length > 0 && (
                <View className="mt-3 p-3 bg-red-50 rounded-lg border border-red-200">
                  <Text className="text-sm font-medium text-red-800 mb-1">⚠️ Contraindicaciones:</Text>
                  {goal.contraindications.map((contra, index) => (
                    <Text key={index} className="text-sm text-red-700">
                      • {contra}
                    </Text>
                  ))}
                </View>
              )}

              {/* Botón de selección */}
              <TouchableOpacity
                className="bg-blue-500 p-3 rounded-lg mt-4 flex-row items-center justify-center"
                onPress={() => onSelect(goal)}
              >
                <CheckCircle size={20} color="white" />
                <Text className="text-white font-semibold ml-2">
                  Seleccionar este objetivo
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <View className="flex-1">
      {/* Header */}
      <View className="mb-4">
        <Text className="text-lg font-bold text-gray-800 mb-2">
          ¿Cuál es el objetivo del servicio?
        </Text>
        <Text className="text-gray-600">
          Selecciona el tipo de coloración que desea la clienta
        </Text>
      </View>

      {/* Filtro de popularidad */}
      <View className="flex-row mb-4">
        <TouchableOpacity
          className={`flex-1 p-3 rounded-lg mr-2 flex-row items-center justify-center ${
            !showAllGoals ? 'bg-blue-500' : 'bg-gray-200'
          }`}
          onPress={() => setShowAllGoals(false)}
        >
          <TrendingUp size={16} color={!showAllGoals ? "white" : "#374151"} />
          <Text className={`font-medium ml-2 ${!showAllGoals ? 'text-white' : 'text-gray-700'}`}>
            Más Populares
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          className={`flex-1 p-3 rounded-lg ml-2 flex-row items-center justify-center ${
            showAllGoals ? 'bg-blue-500' : 'bg-gray-200'
          }`}
          onPress={() => setShowAllGoals(true)}
        >
          <Palette size={16} color={showAllGoals ? "white" : "#374151"} />
          <Text className={`font-medium ml-2 ${showAllGoals ? 'text-white' : 'text-gray-700'}`}>
            Todas las Opciones
          </Text>
        </TouchableOpacity>
      </View>

      {/* Grid de objetivos */}
      <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
        <View className={`flex-row flex-wrap ${maxColumns === 2 ? 'justify-between' : ''}`}>
          {displayGoals.map(renderGoalCard)}
        </View>

        {/* Información adicional */}
        <View className="bg-blue-50 p-4 rounded-lg mt-4 border border-blue-200">
          <View className="flex-row items-center mb-2">
            <Heart size={16} color="#3B82F6" />
            <Text className="text-blue-800 font-medium ml-2">Consejo Profesional</Text>
          </View>
          <Text className="text-blue-700 text-sm">
            Toca cualquier objetivo para ver detalles profesionales, tips y contraindicaciones. 
            La IA ajustará automáticamente la fórmula según tu selección.
          </Text>
        </View>

        {/* Espacio adicional para scroll */}
        <View className="h-20" />
      </ScrollView>
    </View>
  );
};

export default QuickGoalSelector;
