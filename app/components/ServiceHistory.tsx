import React, { useState } from "react";
import { View, Text, ScrollView, TouchableOpacity, Modal } from "react-native";
import { Image } from "expo-image";
import {
  Calendar,
  User,
  Star,
  FileText,
  Camera,
  ChevronRight,
  X,
  Edit3,
  Plus,
  Filter,
  Search,
} from "lucide-react-native";

interface HairService {
  id: string;
  date: string;
  service: string;
  formula?: string;
  stylist: string;
  result: string;
  photos?: string[];
  notes?: string;
  duration?: number;
  price?: number;
  beforePhoto?: string;
  afterPhoto?: string;
}

interface ServiceHistoryProps {
  services?: HairService[];
  onAddService?: () => void;
  onEditService?: (service: HairService) => void;
  onDeleteService?: (serviceId: string) => void;
}

export default function ServiceHistory({
  services = [
    {
      id: "1",
      date: "2024-01-15",
      service: "Balayage + Tratamiento Hidratante",
      formula:
        "L'Oréal Professionnel 7.3 + Oxidante 20vol (1:1.5) + Olaplex No.1",
      stylist: "<PERSON> <PERSON>",
      result: "Excelente",
      duration: 180,
      price: 120,
      beforePhoto:
        "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&q=80",
      afterPhoto:
        "https://images.unsplash.com/photo-1560869713-7d0b29837c64?w=400&q=80",
      photos: [
        "https://images.unsplash.com/photo-1560869713-7d0b29837c64?w=400&q=80",
        "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&q=80",
      ],
      notes:
        "Cliente muy contenta con el resultado. Se logró el tono deseado sin dañar el cabello. Recomendado tratamiento de mantenimiento cada 6 semanas.",
    },
    {
      id: "2",
      date: "2023-12-10",
      service: "Color Global + Corte Bob",
      formula: "Wella Koleston 6/7 Rubio Oscuro Marrón + Oxidante 30vol (1:2)",
      stylist: "Ana López",
      result: "Bueno",
      duration: 120,
      price: 85,
      beforePhoto:
        "https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=400&q=80",
      afterPhoto:
        "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&q=80",
      notes:
        "Necesitó retoque en raíces después de 2 semanas. Cliente satisfecha con el corte pero el color quedó ligeramente más oscuro de lo esperado.",
    },
    {
      id: "3",
      date: "2023-11-05",
      service: "Mechas Californianas + Peinado",
      formula: "Decolorante Blondor + Toner Wella T18 (Lightest Ash Blonde)",
      stylist: "Carlos Ruiz",
      result: "Excelente",
      duration: 150,
      price: 95,
      beforePhoto:
        "https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&q=80",
      afterPhoto:
        "https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=400&q=80",
      notes:
        "Resultado perfecto. Las mechas quedaron naturales y luminosas. Cliente encantada con el efecto degradado.",
    },
    {
      id: "4",
      date: "2023-09-20",
      service: "Tratamiento de Keratina + Corte",
      formula: "Keratina Brasileña Inoar + Champú sin sulfatos",
      stylist: "María Fernández",
      result: "Excelente",
      duration: 240,
      price: 150,
      beforePhoto:
        "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&q=80",
      afterPhoto:
        "https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&q=80",
      notes:
        "Cabello completamente transformado. Reducción del 80% del frizz. Cliente muy satisfecha con la suavidad y brillo obtenidos.",
    },
  ],
  onAddService = () => {},
  onEditService = () => {},
  onDeleteService = () => {},
}: ServiceHistoryProps) {
  const [selectedService, setSelectedService] = useState<HairService | null>(
    null,
  );
  const [showModal, setShowModal] = useState(false);
  const [filterBy, setFilterBy] = useState("all");
  const [sortBy, setSortBy] = useState("date");

  const getResultColor = (result: string) => {
    switch (result) {
      case "Excelente":
        return { bg: "bg-green-100", text: "text-green-700" };
      case "Bueno":
        return { bg: "bg-yellow-100", text: "text-yellow-700" };
      case "Regular":
        return { bg: "bg-orange-100", text: "text-orange-700" };
      default:
        return { bg: "bg-red-100", text: "text-red-700" };
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const openServiceDetail = (service: HairService) => {
    setSelectedService(service);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedService(null);
  };

  const renderServiceCard = (service: HairService) => {
    const resultColors = getResultColor(service.result);

    return (
      <TouchableOpacity
        key={service.id}
        className="bg-white rounded-xl p-4 mb-4 shadow-sm"
        onPress={() => openServiceDetail(service)}
      >
        <View className="flex-row justify-between items-start mb-3">
          <View className="flex-1">
            <Text className="text-lg font-semibold text-gray-900 mb-1">
              {service.service}
            </Text>
            <View className="flex-row items-center">
              <Calendar size={14} color="#6B7280" />
              <Text className="text-gray-500 ml-1">
                {new Date(service.date).toLocaleDateString("es-ES", {
                  day: "numeric",
                  month: "long",
                  year: "numeric",
                })}
              </Text>
            </View>
          </View>
          <View className={`px-3 py-1 rounded-full ${resultColors.bg}`}>
            <Text className={`text-sm font-medium ${resultColors.text}`}>
              {service.result}
            </Text>
          </View>
        </View>

        <View className="flex-row items-center justify-between mb-3">
          <View className="flex-row items-center">
            <User size={14} color="#6B7280" />
            <Text className="text-gray-600 ml-1">{service.stylist}</Text>
          </View>
          {service.duration && (
            <View className="flex-row items-center">
              <Text className="text-gray-500 text-sm">
                {formatDuration(service.duration)}
              </Text>
            </View>
          )}
          {service.price && (
            <Text className="text-indigo-600 font-semibold">
              €{service.price}
            </Text>
          )}
        </View>

        {service.beforePhoto && service.afterPhoto && (
          <View className="flex-row mb-3">
            <View className="flex-1 mr-2">
              <Text className="text-xs text-gray-500 mb-1">Antes</Text>
              <Image
                source={{ uri: service.beforePhoto }}
                className="w-full h-20 rounded-lg"
                contentFit="cover"
              />
            </View>
            <View className="flex-1 ml-2">
              <Text className="text-xs text-gray-500 mb-1">Después</Text>
              <Image
                source={{ uri: service.afterPhoto }}
                className="w-full h-20 rounded-lg"
                contentFit="cover"
              />
            </View>
          </View>
        )}

        {service.formula && (
          <View className="bg-gray-50 p-3 rounded-lg mb-2">
            <Text className="text-xs text-gray-500 uppercase tracking-wide mb-1">
              Fórmula Utilizada
            </Text>
            <Text className="text-gray-800 text-sm" numberOfLines={2}>
              {service.formula}
            </Text>
          </View>
        )}

        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            {service.photos && service.photos.length > 0 && (
              <View className="flex-row items-center mr-4">
                <Camera size={14} color="#6B7280" />
                <Text className="text-gray-500 ml-1 text-sm">
                  {service.photos.length} fotos
                </Text>
              </View>
            )}
            {service.notes && (
              <View className="flex-row items-center">
                <FileText size={14} color="#6B7280" />
                <Text className="text-gray-500 ml-1 text-sm">Con notas</Text>
              </View>
            )}
          </View>
          <ChevronRight size={16} color="#6B7280" />
        </View>
      </TouchableOpacity>
    );
  };

  const renderServiceModal = () => {
    if (!selectedService) return null;

    const resultColors = getResultColor(selectedService.result);

    return (
      <Modal
        visible={showModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-gray-50">
          {/* Header */}
          <View className="bg-white px-4 py-3 border-b border-gray-200">
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-semibold">
                Detalle del Servicio
              </Text>
              <View className="flex-row items-center">
                <TouchableOpacity
                  onPress={() => onEditService(selectedService)}
                  className="mr-4"
                >
                  <Edit3 size={20} color="#6B7280" />
                </TouchableOpacity>
                <TouchableOpacity onPress={closeModal}>
                  <X size={24} color="#374151" />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <ScrollView className="flex-1 p-4">
            {/* Service Info */}
            <View className="bg-white rounded-xl p-4 mb-4">
              <View className="flex-row justify-between items-start mb-3">
                <View className="flex-1">
                  <Text className="text-xl font-bold text-gray-900 mb-2">
                    {selectedService.service}
                  </Text>
                  <Text className="text-gray-500">
                    {new Date(selectedService.date).toLocaleDateString(
                      "es-ES",
                      {
                        weekday: "long",
                        day: "numeric",
                        month: "long",
                        year: "numeric",
                      },
                    )}
                  </Text>
                </View>
                <View className={`px-3 py-1 rounded-full ${resultColors.bg}`}>
                  <Text className={`text-sm font-medium ${resultColors.text}`}>
                    {selectedService.result}
                  </Text>
                </View>
              </View>

              <View className="flex-row justify-between">
                <View>
                  <Text className="text-gray-500 text-sm">Estilista</Text>
                  <Text className="font-semibold">
                    {selectedService.stylist}
                  </Text>
                </View>
                {selectedService.duration && (
                  <View>
                    <Text className="text-gray-500 text-sm">Duración</Text>
                    <Text className="font-semibold">
                      {formatDuration(selectedService.duration)}
                    </Text>
                  </View>
                )}
                {selectedService.price && (
                  <View>
                    <Text className="text-gray-500 text-sm">Precio</Text>
                    <Text className="font-semibold text-indigo-600">
                      €{selectedService.price}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* Before/After Photos */}
            {selectedService.beforePhoto && selectedService.afterPhoto && (
              <View className="bg-white rounded-xl p-4 mb-4">
                <Text className="text-lg font-semibold mb-3">
                  Antes y Después
                </Text>
                <View className="flex-row">
                  <View className="flex-1 mr-2">
                    <Text className="text-sm text-gray-500 mb-2">Antes</Text>
                    <Image
                      source={{ uri: selectedService.beforePhoto }}
                      className="w-full h-40 rounded-lg"
                      contentFit="cover"
                    />
                  </View>
                  <View className="flex-1 ml-2">
                    <Text className="text-sm text-gray-500 mb-2">Después</Text>
                    <Image
                      source={{ uri: selectedService.afterPhoto }}
                      className="w-full h-40 rounded-lg"
                      contentFit="cover"
                    />
                  </View>
                </View>
              </View>
            )}

            {/* Formula */}
            {selectedService.formula && (
              <View className="bg-white rounded-xl p-4 mb-4">
                <Text className="text-lg font-semibold mb-3">
                  Fórmula Utilizada
                </Text>
                <View className="bg-gray-50 p-3 rounded-lg">
                  <Text className="text-gray-800">
                    {selectedService.formula}
                  </Text>
                </View>
              </View>
            )}

            {/* Additional Photos */}
            {selectedService.photos && selectedService.photos.length > 0 && (
              <View className="bg-white rounded-xl p-4 mb-4">
                <Text className="text-lg font-semibold mb-3">
                  Galería ({selectedService.photos.length} fotos)
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View className="flex-row">
                    {selectedService.photos.map((photo, index) => (
                      <Image
                        key={index}
                        source={{ uri: photo }}
                        className="w-24 h-24 rounded-lg mr-2"
                        contentFit="cover"
                      />
                    ))}
                  </View>
                </ScrollView>
              </View>
            )}

            {/* Notes */}
            {selectedService.notes && (
              <View className="bg-white rounded-xl p-4 mb-4">
                <Text className="text-lg font-semibold mb-3">
                  Notas del Estilista
                </Text>
                <Text className="text-gray-700 leading-6">
                  {selectedService.notes}
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white px-4 py-3 border-b border-gray-200">
        <View className="flex-row items-center justify-between mb-3">
          <Text className="text-xl font-bold">Historial de Servicios</Text>
          <TouchableOpacity
            onPress={onAddService}
            className="bg-indigo-600 rounded-lg px-4 py-2 flex-row items-center"
          >
            <Plus size={16} color="white" />
            <Text className="text-white font-semibold ml-1">Agregar</Text>
          </TouchableOpacity>
        </View>

        {/* Filters */}
        <View className="flex-row items-center">
          <TouchableOpacity className="flex-row items-center bg-gray-100 rounded-lg px-3 py-2 mr-2">
            <Filter size={14} color="#6B7280" />
            <Text className="text-gray-600 ml-1 text-sm">Filtrar</Text>
          </TouchableOpacity>
          <TouchableOpacity className="flex-row items-center bg-gray-100 rounded-lg px-3 py-2">
            <Search size={14} color="#6B7280" />
            <Text className="text-gray-600 ml-1 text-sm">Buscar</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Stats */}
      <View className="bg-white mx-4 mt-4 rounded-xl p-4">
        <View className="flex-row justify-between">
          <View className="items-center">
            <Text className="text-2xl font-bold text-indigo-600">
              {services.length}
            </Text>
            <Text className="text-gray-500 text-sm">Servicios</Text>
          </View>
          <View className="items-center">
            <Text className="text-2xl font-bold text-green-600">
              {services.filter((s) => s.result === "Excelente").length}
            </Text>
            <Text className="text-gray-500 text-sm">Excelentes</Text>
          </View>
          <View className="items-center">
            <Text className="text-2xl font-bold text-purple-600">
              €{services.reduce((sum, s) => sum + (s.price || 0), 0)}
            </Text>
            <Text className="text-gray-500 text-sm">Total</Text>
          </View>
          <View className="items-center">
            <Text className="text-2xl font-bold text-blue-600">
              {Math.round(
                services.reduce((sum, s) => sum + (s.duration || 0), 0) / 60,
              )}
              h
            </Text>
            <Text className="text-gray-500 text-sm">Tiempo</Text>
          </View>
        </View>
      </View>

      {/* Services List */}
      <ScrollView
        className="flex-1 px-4 pt-4"
        showsVerticalScrollIndicator={false}
      >
        {services.length > 0 ? (
          services.map(renderServiceCard)
        ) : (
          <View className="bg-white rounded-xl p-8 items-center">
            <FileText size={48} color="#D1D5DB" />
            <Text className="text-gray-500 text-lg font-medium mt-4 mb-2">
              No hay servicios registrados
            </Text>
            <Text className="text-gray-400 text-center mb-6">
              Comienza agregando el primer servicio realizado
            </Text>
            <TouchableOpacity
              onPress={onAddService}
              className="bg-indigo-600 rounded-lg px-6 py-3"
            >
              <Text className="text-white font-semibold">Agregar Servicio</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Service Detail Modal */}
      {renderServiceModal()}
    </View>
  );
}
