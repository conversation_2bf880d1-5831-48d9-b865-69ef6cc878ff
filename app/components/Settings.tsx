import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Modal,
  TextInput,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import {
  ArrowLeft,
  User,
  Building2,
  Palette,
  Bell,
  Shield,
  Globe,
  ChevronRight,
  Check,
  Search,
  X,
  Trash2,
} from "lucide-react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

interface Brand {
  id: string;
  name: string;
  productLines: ProductLine[];
}

interface ProductLine {
  id: string;
  name: string;
  description: string;
}

interface Settings {
  profile: {
    name: string;
    email: string;
    phone: string;
  };
  salon: {
    name: string;
    address: string;
    country: string;
    language: string;
  };
  brands: {
    selectedBrands: string[];
    selectedProductLines: string[];
  };
  notifications: {
    appointments: boolean;
    reminders: boolean;
    marketing: boolean;
  };
  privacy: {
    dataSharing: boolean;
    analytics: boolean;
  };
}

// Simplified brand list with most popular brands
const AVAILABLE_BRANDS: Brand[] = [
  {
    id: "loreal",
    name: "L'Oréal Professionnel",
    productLines: [
      { id: "majirel", name: "Majirel", description: "Coloración permanente" },
      { id: "inoa", name: "INOA", description: "Sin amoníaco" },
      { id: "dialight", name: "Dialight", description: "Tono sobre tono" },
    ],
  },
  {
    id: "wella",
    name: "Wella Professionals",
    productLines: [
      {
        id: "koleston",
        name: "Koleston Perfect",
        description: "Coloración permanente",
      },
      {
        id: "illumina",
        name: "Illumina Color",
        description: "Tecnología Microlight",
      },
      {
        id: "color-touch",
        name: "Color Touch",
        description: "Tono sobre tono",
      },
    ],
  },
  {
    id: "schwarzkopf",
    name: "Schwarzkopf Professional",
    productLines: [
      {
        id: "igora-royal",
        name: "Igora Royal",
        description: "Coloración permanente",
      },
      {
        id: "igora-vibrance",
        name: "Igora Vibrance",
        description: "Tono sobre tono",
      },
      {
        id: "blondme",
        name: "BlondMe",
        description: "Decoloración y tonos rubios",
      },
    ],
  },
  {
    id: "redken",
    name: "Redken",
    productLines: [
      {
        id: "chromatics",
        name: "Chromatics",
        description: "Color sin amoníaco",
      },
      { id: "shades-eq", name: "Shades EQ", description: "Gloss acidificante" },
    ],
  },
  {
    id: "matrix",
    name: "Matrix",
    productLines: [
      { id: "socolor", name: "SoColor", description: "Coloración permanente" },
      { id: "color-sync", name: "Color Sync", description: "Tono sobre tono" },
    ],
  },
  {
    id: "goldwell",
    name: "Goldwell",
    productLines: [
      { id: "topchic", name: "Topchic", description: "Coloración permanente" },
      { id: "colorance", name: "Colorance", description: "Tono sobre tono" },
    ],
  },
  {
    id: "joico",
    name: "Joico",
    productLines: [
      { id: "lumishine", name: "LumiShine", description: "Color líquido" },
      {
        id: "vero-k-pak",
        name: "Vero K-PAK",
        description: "Coloración permanente",
      },
    ],
  },
  {
    id: "pravana",
    name: "Pravana",
    productLines: [
      {
        id: "chromasilk",
        name: "ChromaSilk",
        description: "Coloración permanente",
      },
      { id: "vivids", name: "Vivids", description: "Colores fantasía" },
    ],
  },
];

const COUNTRIES = [
  "España",
  "México",
  "Argentina",
  "Colombia",
  "Chile",
  "Perú",
  "Venezuela",
];

const LANGUAGES = [
  { code: "es", name: "Español" },
  { code: "en", name: "English" },
  { code: "pt", name: "Português" },
];

export default function Settings() {
  const router = useRouter();
  const [settings, setSettings] = useState<Settings>({
    profile: {
      name: "María García",
      email: "<EMAIL>",
      phone: "+34 123 456 789",
    },
    salon: {
      name: "Salón María",
      address: "Calle Principal 123, Madrid",
      country: "España",
      language: "es",
    },
    brands: {
      selectedBrands: ["loreal", "wella"],
      selectedProductLines: ["majirel", "inoa", "koleston"],
    },
    notifications: {
      appointments: true,
      reminders: true,
      marketing: false,
    },
    privacy: {
      dataSharing: false,
      analytics: true,
    },
  });

  const [showBrandModal, setShowBrandModal] = useState(false);
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem("salonier_settings");
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error("Error loading settings:", error);
    }
  };

  const saveSettings = async (newSettings: Settings) => {
    try {
      await AsyncStorage.setItem(
        "salonier_settings",
        JSON.stringify(newSettings),
      );
      setSettings(newSettings);
    } catch (error) {
      console.error("Error saving settings:", error);
      Alert.alert("Error", "No se pudieron guardar los ajustes");
    }
  };

  const toggleBrand = (brandId: string) => {
    const isCurrentlySelected =
      settings.brands.selectedBrands.includes(brandId);
    const brand = AVAILABLE_BRANDS.find((b) => b.id === brandId);

    if (!brand) return;

    let newSelectedBrands: string[];
    let newSelectedProductLines: string[];

    if (isCurrentlySelected) {
      // Remove brand and all its product lines
      newSelectedBrands = settings.brands.selectedBrands.filter(
        (id) => id !== brandId,
      );
      const brandProductLineIds = brand.productLines.map((pl) => pl.id);
      newSelectedProductLines = settings.brands.selectedProductLines.filter(
        (id) => !brandProductLineIds.includes(id),
      );
    } else {
      // Add brand and all its product lines
      newSelectedBrands = [...settings.brands.selectedBrands, brandId];
      const brandProductLineIds = brand.productLines.map((pl) => pl.id);
      newSelectedProductLines = [
        ...settings.brands.selectedProductLines,
        ...brandProductLineIds,
      ];
    }

    const newSettings = {
      ...settings,
      brands: {
        selectedBrands: newSelectedBrands,
        selectedProductLines: newSelectedProductLines,
      },
    };

    saveSettings(newSettings);
  };

  const toggleProductLine = (productLineId: string, brandId: string) => {
    const isCurrentlySelected =
      settings.brands.selectedProductLines.includes(productLineId);
    const brand = AVAILABLE_BRANDS.find((b) => b.id === brandId);

    if (!brand) return;

    let newSelectedProductLines: string[];
    let newSelectedBrands: string[];

    if (isCurrentlySelected) {
      // Remove product line
      newSelectedProductLines = settings.brands.selectedProductLines.filter(
        (id) => id !== productLineId,
      );

      // Check if brand should be removed (no product lines left)
      const remainingBrandProductLines = brand.productLines.filter((pl) =>
        newSelectedProductLines.includes(pl.id),
      );

      if (remainingBrandProductLines.length === 0) {
        newSelectedBrands = settings.brands.selectedBrands.filter(
          (id) => id !== brandId,
        );
      } else {
        newSelectedBrands = settings.brands.selectedBrands;
      }
    } else {
      // Add product line
      newSelectedProductLines = [
        ...settings.brands.selectedProductLines,
        productLineId,
      ];

      // Add brand if not already selected
      if (!settings.brands.selectedBrands.includes(brandId)) {
        newSelectedBrands = [...settings.brands.selectedBrands, brandId];
      } else {
        newSelectedBrands = settings.brands.selectedBrands;
      }
    }

    const newSettings = {
      ...settings,
      brands: {
        selectedBrands: newSelectedBrands,
        selectedProductLines: newSelectedProductLines,
      },
    };

    saveSettings(newSettings);
  };

  const clearAllSelections = () => {
    Alert.alert(
      "Borrar Selecciones",
      "¿Estás seguro de que quieres deseleccionar todas las marcas?",
      [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Borrar",
          style: "destructive",
          onPress: async () => {
            try {
              // Create new settings with empty brand selections
              const clearedSettings = {
                ...settings,
                brands: {
                  selectedBrands: [],
                  selectedProductLines: [],
                },
              };

              // Use the existing saveSettings function which handles both AsyncStorage and state
              await saveSettings(clearedSettings);

              // Show confirmation that the action was completed
              Alert.alert("Éxito", "Todas las marcas han sido deseleccionadas");
            } catch (error) {
              console.error("Error clearing selections:", error);
              Alert.alert("Error", "No se pudieron borrar las selecciones");
            }
          },
        },
      ],
    );
  };

  const getFilteredBrands = () => {
    if (!searchQuery.trim()) {
      return AVAILABLE_BRANDS;
    }

    const query = searchQuery.toLowerCase();
    return AVAILABLE_BRANDS.filter((brand) =>
      brand.name.toLowerCase().includes(query),
    );
  };

  const updateNotificationSetting = (
    key: keyof typeof settings.notifications,
    value: boolean,
  ) => {
    const newSettings = {
      ...settings,
      notifications: {
        ...settings.notifications,
        [key]: value,
      },
    };
    saveSettings(newSettings);
  };

  const updatePrivacySetting = (
    key: keyof typeof settings.privacy,
    value: boolean,
  ) => {
    const newSettings = {
      ...settings,
      privacy: {
        ...settings.privacy,
        [key]: value,
      },
    };
    saveSettings(newSettings);
  };

  const SettingItem = ({
    icon,
    title,
    subtitle,
    onPress,
    rightElement,
  }: {
    icon: React.ReactNode;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
  }) => (
    <TouchableOpacity
      className="flex-row items-center p-4 bg-white border-b border-gray-100"
      onPress={onPress}
      disabled={!onPress}
    >
      <View className="h-10 w-10 rounded-full bg-gray-100 items-center justify-center mr-3">
        {icon}
      </View>
      <View className="flex-1">
        <Text className="text-gray-800 font-medium">{title}</Text>
        {subtitle && (
          <Text className="text-gray-500 text-sm mt-1">{subtitle}</Text>
        )}
      </View>
      {rightElement || (onPress && <ChevronRight size={20} color="#9ca3af" />)}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="flex-row items-center px-4 py-3 bg-white border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()} className="mr-3">
          <ArrowLeft size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-bold text-gray-800">Configuración</Text>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <View className="mt-6">
          <Text className="text-lg font-bold text-gray-800 px-4 mb-3">
            Perfil
          </Text>
          <SettingItem
            icon={<User size={20} color="#6b7280" />}
            title="Información Personal"
            subtitle={`${settings.profile.name} • ${settings.profile.email}`}
            onPress={() => Alert.alert("Próximamente", "Función en desarrollo")}
          />
        </View>

        {/* Salon Section */}
        <View className="mt-6">
          <Text className="text-lg font-bold text-gray-800 px-4 mb-3">
            Salón
          </Text>
          <SettingItem
            icon={<Building2 size={20} color="#6b7280" />}
            title="Información del Salón"
            subtitle={settings.salon.name}
            onPress={() => Alert.alert("Próximamente", "Función en desarrollo")}
          />
          <SettingItem
            icon={<Globe size={20} color="#6b7280" />}
            title="País"
            subtitle={settings.salon.country}
            onPress={() => setShowCountryModal(true)}
          />
          <SettingItem
            icon={<Globe size={20} color="#6b7280" />}
            title="Idioma"
            subtitle={
              LANGUAGES.find((l) => l.code === settings.salon.language)?.name
            }
            onPress={() => setShowLanguageModal(true)}
          />
        </View>

        {/* Brands & Products Section */}
        <View className="mt-6">
          <Text className="text-lg font-bold text-gray-800 px-4 mb-3">
            Marcas y Productos
          </Text>
          <SettingItem
            icon={<Palette size={20} color="#6b7280" />}
            title="Marcas Disponibles"
            subtitle={`${settings.brands.selectedBrands.length} marcas seleccionadas`}
            onPress={() => setShowBrandModal(true)}
          />
        </View>

        {/* Notifications Section */}
        <View className="mt-6">
          <Text className="text-lg font-bold text-gray-800 px-4 mb-3">
            Notificaciones
          </Text>
          <SettingItem
            icon={<Bell size={20} color="#6b7280" />}
            title="Recordatorios de Citas"
            rightElement={
              <Switch
                value={settings.notifications.appointments}
                onValueChange={(value) =>
                  updateNotificationSetting("appointments", value)
                }
                trackColor={{ false: "#d1d5db", true: "#4f46e5" }}
                thumbColor={
                  settings.notifications.appointments ? "#ffffff" : "#f3f4f6"
                }
              />
            }
          />
          <SettingItem
            icon={<Bell size={20} color="#6b7280" />}
            title="Recordatorios Generales"
            rightElement={
              <Switch
                value={settings.notifications.reminders}
                onValueChange={(value) =>
                  updateNotificationSetting("reminders", value)
                }
                trackColor={{ false: "#d1d5db", true: "#4f46e5" }}
                thumbColor={
                  settings.notifications.reminders ? "#ffffff" : "#f3f4f6"
                }
              />
            }
          />
          <SettingItem
            icon={<Bell size={20} color="#6b7280" />}
            title="Marketing"
            rightElement={
              <Switch
                value={settings.notifications.marketing}
                onValueChange={(value) =>
                  updateNotificationSetting("marketing", value)
                }
                trackColor={{ false: "#d1d5db", true: "#4f46e5" }}
                thumbColor={
                  settings.notifications.marketing ? "#ffffff" : "#f3f4f6"
                }
              />
            }
          />
        </View>

        {/* Privacy Section */}
        <View className="mt-6 mb-8">
          <Text className="text-lg font-bold text-gray-800 px-4 mb-3">
            Privacidad
          </Text>
          <SettingItem
            icon={<Shield size={20} color="#6b7280" />}
            title="Compartir Datos"
            subtitle="Permitir compartir datos anónimos para mejorar el servicio"
            rightElement={
              <Switch
                value={settings.privacy.dataSharing}
                onValueChange={(value) =>
                  updatePrivacySetting("dataSharing", value)
                }
                trackColor={{ false: "#d1d5db", true: "#4f46e5" }}
                thumbColor={
                  settings.privacy.dataSharing ? "#ffffff" : "#f3f4f6"
                }
              />
            }
          />
          <SettingItem
            icon={<Shield size={20} color="#6b7280" />}
            title="Analíticas"
            subtitle="Permitir recopilar datos de uso para mejorar la aplicación"
            rightElement={
              <Switch
                value={settings.privacy.analytics}
                onValueChange={(value) =>
                  updatePrivacySetting("analytics", value)
                }
                trackColor={{ false: "#d1d5db", true: "#4f46e5" }}
                thumbColor={settings.privacy.analytics ? "#ffffff" : "#f3f4f6"}
              />
            }
          />
        </View>
      </ScrollView>

      {/* Simplified Brand Selection Modal */}
      <Modal
        visible={showBrandModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView className="flex-1 bg-white">
          {/* Header */}
          <View className="flex-row items-center justify-between px-4 py-3 bg-white border-b border-gray-200">
            <Text className="text-xl font-bold text-gray-800">
              Seleccionar Marcas
            </Text>
            <TouchableOpacity onPress={() => setShowBrandModal(false)}>
              <Text className="text-indigo-600 font-medium">Cerrar</Text>
            </TouchableOpacity>
          </View>

          {/* Simple Summary */}
          <View className="bg-indigo-50 p-4 border-b border-indigo-100">
            <View className="flex-row items-center justify-between">
              <Text className="text-indigo-800 font-medium">
                {settings.brands.selectedBrands.length} marcas seleccionadas
              </Text>
              {(settings.brands.selectedBrands.length > 0 ||
                settings.brands.selectedProductLines.length > 0) && (
                <TouchableOpacity
                  onPress={clearAllSelections}
                  className="bg-red-100 px-3 py-2 rounded-lg flex-row items-center"
                  activeOpacity={0.7}
                >
                  <Trash2 size={16} color="#dc2626" />
                  <Text className="text-red-700 font-medium ml-2">
                    Borrar Todo
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Description */}
          <View className="p-4 bg-gray-50">
            <Text className="text-gray-600 text-center">
              Selecciona las marcas que utilizas en tu salón. Al seleccionar una
              marca, todas sus líneas de productos se incluirán automáticamente.
            </Text>
          </View>

          {/* Search */}
          <View className="p-4">
            <View className="flex-row items-center bg-gray-100 rounded-lg px-3 py-2">
              <Search size={20} color="#9ca3af" />
              <TextInput
                className="flex-1 ml-2 py-1"
                placeholder="Buscar marcas..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery("")}>
                  <X size={20} color="#9ca3af" />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Brand List */}
          <ScrollView className="flex-1 px-4">
            {getFilteredBrands().length === 0 ? (
              <View className="items-center justify-center py-8">
                <Text className="text-gray-500 text-center">
                  No se encontraron marcas que coincidan con tu búsqueda
                </Text>
              </View>
            ) : (
              getFilteredBrands().map((brand) => {
                const isSelected = settings.brands.selectedBrands.includes(
                  brand.id,
                );

                return (
                  <TouchableOpacity
                    key={brand.id}
                    className={`p-4 mb-3 rounded-xl border-2 ${
                      isSelected
                        ? "bg-indigo-50 border-indigo-200"
                        : "bg-white border-gray-200"
                    }`}
                    onPress={() => toggleBrand(brand.id)}
                  >
                    <View className="flex-row items-center justify-between">
                      <View className="flex-1">
                        <Text
                          className={`text-lg font-bold ${
                            isSelected ? "text-indigo-800" : "text-gray-800"
                          }`}
                        >
                          {brand.name}
                        </Text>
                        <Text className="text-gray-500 text-sm mt-1">
                          {brand.productLines.length} líneas de productos
                          incluidas
                        </Text>

                        {/* Show product lines with individual selection */}
                        {isSelected && (
                          <View className="mt-3">
                            <Text className="text-indigo-700 font-medium text-sm mb-2">
                              Líneas de productos:
                            </Text>
                            <View className="space-y-2">
                              {brand.productLines.map((productLine) => {
                                const isProductLineSelected =
                                  settings.brands.selectedProductLines.includes(
                                    productLine.id,
                                  );
                                return (
                                  <TouchableOpacity
                                    key={productLine.id}
                                    className="flex-row items-center justify-between py-2 px-3 bg-white rounded-lg border border-gray-200"
                                    onPress={() =>
                                      toggleProductLine(
                                        productLine.id,
                                        brand.id,
                                      )
                                    }
                                  >
                                    <View className="flex-1">
                                      <Text className="text-gray-800 font-medium text-sm">
                                        {productLine.name}
                                      </Text>
                                      <Text className="text-gray-500 text-xs">
                                        {productLine.description}
                                      </Text>
                                    </View>
                                    <View
                                      className={`w-5 h-5 rounded border-2 items-center justify-center ${
                                        isProductLineSelected
                                          ? "bg-indigo-600 border-indigo-600"
                                          : "border-gray-300"
                                      }`}
                                    >
                                      {isProductLineSelected && (
                                        <Check size={12} color="white" />
                                      )}
                                    </View>
                                  </TouchableOpacity>
                                );
                              })}
                            </View>
                          </View>
                        )}
                      </View>

                      <View
                        className={`w-8 h-8 rounded-full border-2 items-center justify-center ml-3 ${
                          isSelected
                            ? "bg-indigo-600 border-indigo-600"
                            : "border-gray-300"
                        }`}
                      >
                        {isSelected && <Check size={18} color="white" />}
                      </View>
                    </View>
                  </TouchableOpacity>
                );
              })
            )}
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Country Selection Modal */}
      <Modal
        visible={showCountryModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView className="flex-1 bg-white">
          <View className="flex-row items-center justify-between px-4 py-3 border-b border-gray-200">
            <Text className="text-xl font-bold text-gray-800">
              Seleccionar País
            </Text>
            <TouchableOpacity onPress={() => setShowCountryModal(false)}>
              <Text className="text-indigo-600 font-medium">Cerrar</Text>
            </TouchableOpacity>
          </View>

          <ScrollView className="flex-1">
            {COUNTRIES.map((country) => (
              <TouchableOpacity
                key={country}
                className="flex-row items-center justify-between p-4 border-b border-gray-100"
                onPress={() => {
                  const newSettings = {
                    ...settings,
                    salon: {
                      ...settings.salon,
                      country,
                    },
                  };
                  saveSettings(newSettings);
                  setShowCountryModal(false);
                }}
              >
                <Text className="text-gray-800">{country}</Text>
                {settings.salon.country === country && (
                  <Check size={20} color="#4f46e5" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Language Selection Modal */}
      <Modal
        visible={showLanguageModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView className="flex-1 bg-white">
          <View className="flex-row items-center justify-between px-4 py-3 border-b border-gray-200">
            <Text className="text-xl font-bold text-gray-800">
              Seleccionar Idioma
            </Text>
            <TouchableOpacity onPress={() => setShowLanguageModal(false)}>
              <Text className="text-indigo-600 font-medium">Cerrar</Text>
            </TouchableOpacity>
          </View>

          <ScrollView className="flex-1">
            {LANGUAGES.map((language) => (
              <TouchableOpacity
                key={language.code}
                className="flex-row items-center justify-between p-4 border-b border-gray-100"
                onPress={() => {
                  const newSettings = {
                    ...settings,
                    salon: {
                      ...settings.salon,
                      language: language.code,
                    },
                  };
                  saveSettings(newSettings);
                  setShowLanguageModal(false);
                }}
              >
                <Text className="text-gray-800">{language.name}</Text>
                {settings.salon.language === language.code && (
                  <Check size={20} color="#4f46e5" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}
