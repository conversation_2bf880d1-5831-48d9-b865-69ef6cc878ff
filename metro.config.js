const { getDefaultConfig } = require("expo/metro-config");
const { withNativeWind } = require("nativewind/metro");

const config = getDefaultConfig(__dirname);

// Configure resolver for better platform handling
config.resolver.platforms = ["ios", "android", "native", "web"];
config.resolver.resolverMainFields = ["react-native", "browser", "main"];

// Add resolver to handle tempo-devtools safely
config.resolver.resolverMainFields = ["react-native", "browser", "main"];
config.resolver.alias = {
  ...config.resolver.alias,
  // Alias tempo-devtools to our wrapper for non-web platforms
  "tempo-devtools": require.resolve("./utils/tempo-devtools-wrapper.ts"),
};

// Block tempo-devtools from being bundled in non-web environments
const originalResolveRequest = config.resolver.resolveRequest;
config.resolver.resolveRequest = (context, moduleName, platform) => {
  if (moduleName === "tempo-devtools" && platform !== "web") {
    // Return our wrapper instead
    return {
      filePath: require.resolve("./utils/tempo-devtools-wrapper.ts"),
      type: "sourceFile",
    };
  }

  if (originalResolveRequest) {
    return originalResolveRequest(context, moduleName, platform);
  }

  return context.resolveRequest(context, moduleName, platform);
};

module.exports = withNativeWind(config, { input: "./global.css" });
