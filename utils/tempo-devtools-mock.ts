import { Platform } from "react-native";

/**
 * Mock implementation of tempo-devtools for React Native environments
 * This prevents "document is not defined" errors when tempo-devtools is imported
 */

// Mock TempoDevtools class
export class TempoDevtools {
  static init() {
    if (Platform.OS !== "web") {
      console.log("TempoDevtools mock - not available in mobile environment");
      return;
    }
    
    // Only try to initialize real tempo-devtools in web environment
    try {
      const realTempoDevtools = require("tempo-devtools/dist/index.js");
      if (realTempoDevtools && realTempoDevtools.TempoDevtools) {
        realTempoDevtools.TempoDevtools.init();
      }
    } catch (error) {
      console.warn("Real TempoDevtools initialization failed:", error);
    }
  }

  static configure() {
    if (Platform.OS !== "web") {
      console.log("TempoDevtools configure mock - not available in mobile environment");
      return;
    }
  }

  static start() {
    if (Platform.OS !== "web") {
      console.log("TempoDevtools start mock - not available in mobile environment");
      return;
    }
  }

  static stop() {
    if (Platform.OS !== "web") {
      console.log("TempoDevtools stop mock - not available in mobile environment");
      return;
    }
  }
}

// Default export
export default {
  TempoDevtools,
  init: TempoDevtools.init,
  configure: TempoDevtools.configure,
  start: TempoDevtools.start,
  stop: TempoDevtools.stop,
};

// Named exports for compatibility
export const init = TempoDevtools.init;
export const configure = TempoDevtools.configure;
export const start = TempoDevtools.start;
export const stop = TempoDevtools.stop;
